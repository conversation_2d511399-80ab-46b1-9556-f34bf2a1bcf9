#!/usr/bin/env python3
"""
重新收集所有数据，确保获取完整数量
"""
from datetime import datetime
from core.data_collector import DataCollector

def recollect_all_data():
    """重新收集所有数据"""
    print("🔄 === 重新收集所有数据 ===\n")
    
    collector = DataCollector()
    try:
        # 获取今天日期
        today = datetime.now().strftime('%Y%m%d')
        print(f"📅 收集日期: {today}\n")
        
        # 1. 收集板块数据（使用修复后的核心板块数据）
        print("📊 1. 收集板块数据...")
        plate_success = collector.collect_plate_data(today)
        print(f"   结果: {'✅ 成功' if plate_success else '❌ 失败'}")
        
        # 2. 收集板块强度数据
        print("💪 2. 收集板块强度数据...")
        strength_success = collector.collect_plate_strength_data(today)
        print(f"   结果: {'✅ 成功' if strength_success else '❌ 失败'}")
        
        # 3. 收集龙虎榜数据
        print("🐉 3. 收集龙虎榜数据...")
        lhb_success = collector.collect_longhubang_data(today)
        print(f"   结果: {'✅ 成功' if lhb_success else '❌ 失败'}")
        
        # 4. 收集一些热门股票数据
        print("📈 4. 收集热门股票数据...")
        hot_stocks = [
            '600887', '300015', '002304', '000725', '600276',  # 现有的
            '000001', '000002', '600000', '600036', '000858',  # 新增一些
            '002415', '002594', '300059', '600519', '000002'   # 更多股票
        ]
        
        stock_results = collector.batch_collect_stocks(hot_stocks, today, max_workers=3)
        print(f"   结果: ✅ 成功{stock_results['success']}条, ❌ 失败{stock_results['failed']}条")
        
        print(f"\n🎯 === 收集完成 ===")
        print(f"📊 板块数据: {'✅' if plate_success else '❌'}")
        print(f"💪 板块强度: {'✅' if strength_success else '❌'}")
        print(f"🐉 龙虎榜: {'✅' if lhb_success else '❌'}")
        print(f"📈 股票数据: ✅ {stock_results['success']}条")
        
        # 查看最终数据统计
        print(f"\n📋 === 最终数据统计 ===")
        from simple_data_view import view_data
        view_data()
        
    except Exception as e:
        print(f"❌ 收集过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        collector.close()

if __name__ == "__main__":
    recollect_all_data()
