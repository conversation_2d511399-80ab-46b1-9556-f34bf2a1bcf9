2025-06-30 19:11:37 - __main__ - INFO - 初始化数据库...
2025-06-30 19:11:38 - __main__ - INFO - 数据库初始化完成
2025-06-30 19:11:49 - __main__ - INFO - 测试开盘啦API连接...
2025-06-30 19:11:49 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 19:11:50 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:11:50 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '000001'}
2025-06-30 19:11:50 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 19:11:50 - core.http_client - INFO - API连接测试成功
2025-06-30 19:11:50 - __main__ - INFO - ✅ API连接测试成功
2025-06-30 19:12:03 - __main__ - INFO - === 开始基础功能测试 ===
2025-06-30 19:12:03 - __main__ - INFO - 1. 测试API连接...
2025-06-30 19:12:03 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 19:12:04 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:12:04 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '000001'}
2025-06-30 19:12:04 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 19:12:04 - core.http_client - INFO - API连接测试成功
2025-06-30 19:12:04 - __main__ - INFO -    API连接: ✅ 成功
2025-06-30 19:12:04 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 19:12:04 - __main__ - INFO - 2. 测试数据库...
2025-06-30 19:12:04 - __main__ - ERROR -    数据库连接异常: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
2025-06-30 19:12:04 - __main__ - INFO - 3. 测试数据采集...
2025-06-30 19:12:04 - utils.logger - INFO - 调用函数: get_stock_list_from_tushare
2025-06-30 19:12:04 - core.data_collector - INFO - 开始从Tushare获取股票列表
2025-06-30 19:12:04 - core.data_collector - INFO - 获取到 10 只股票
2025-06-30 19:12:04 - utils.logger - INFO - 函数 get_stock_list_from_tushare 执行成功
2025-06-30 19:12:04 - __main__ - INFO -    获取股票列表: ✅ 成功 (10 只)
2025-06-30 19:12:04 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 19:12:04 - __main__ - INFO - === 基础功能测试完成 ===
2025-06-30 19:12:53 - __main__ - INFO - === 开始基础功能测试 ===
2025-06-30 19:12:53 - __main__ - INFO - 1. 测试API连接...
2025-06-30 19:12:53 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 19:12:54 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:12:54 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '000001'}
2025-06-30 19:12:54 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 19:12:54 - core.http_client - INFO - API连接测试成功
2025-06-30 19:12:54 - __main__ - INFO -    API连接: ✅ 成功
2025-06-30 19:12:54 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 19:12:54 - __main__ - INFO - 2. 测试数据库...
2025-06-30 19:12:54 - __main__ - INFO -    数据库连接: ✅ 成功
2025-06-30 19:12:54 - __main__ - INFO - 3. 测试数据采集...
2025-06-30 19:12:54 - utils.logger - INFO - 调用函数: get_stock_list_from_tushare
2025-06-30 19:12:54 - core.data_collector - INFO - 开始从Tushare获取股票列表
2025-06-30 19:12:54 - core.data_collector - INFO - 获取到 10 只股票
2025-06-30 19:12:54 - utils.logger - INFO - 函数 get_stock_list_from_tushare 执行成功
2025-06-30 19:12:54 - __main__ - INFO -    获取股票列表: ✅ 成功 (10 只)
2025-06-30 19:12:54 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 19:12:54 - __main__ - INFO - === 基础功能测试完成 ===
2025-06-30 19:13:12 - __main__ - INFO - 开始采集今日数据: 20250630
2025-06-30 19:13:12 - utils.logger - INFO - 调用函数: get_stock_list_from_tushare
2025-06-30 19:13:12 - core.data_collector - INFO - 开始从Tushare获取股票列表
2025-06-30 19:13:12 - core.data_collector - INFO - 获取到 10 只股票
2025-06-30 19:13:12 - utils.logger - INFO - 函数 get_stock_list_from_tushare 执行成功
2025-06-30 19:13:12 - __main__ - INFO - 获取到 10 只股票
2025-06-30 19:13:12 - utils.logger - INFO - 调用函数: batch_collect_stocks
2025-06-30 19:13:12 - core.data_collector - INFO - 开始批量采集 10 只股票数据
2025-06-30 19:13:12 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:13:12 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 19:13:12 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:13:12 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:13:12 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:13:12 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:13:12 - core.http_client - INFO - 获取股票数据: 000002, 日期: 20250630
2025-06-30 19:13:12 - core.http_client - INFO - 获取股票数据: 000858, 日期: 20250630
2025-06-30 19:13:12 - core.http_client - INFO - 获取股票数据: 002415, 日期: 20250630
2025-06-30 19:13:12 - core.http_client - INFO - 获取股票数据: 600000, 日期: 20250630
2025-06-30 19:13:13 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:13:13 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:13:13 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '000002'}
2025-06-30 19:13:13 - core.http_client - DEBUG - 股票 000002 数据获取成功
2025-06-30 19:13:13 - core.data_collector - DEBUG - 股票 000002 数据保存成功
2025-06-30 19:13:13 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '000001'}
2025-06-30 19:13:13 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 19:13:13 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:13:13 - core.data_collector - DEBUG - 股票 000001 数据保存成功
2025-06-30 19:13:13 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:13:13 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:13:13 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:13:13 - core.http_client - INFO - 获取股票数据: 600036, 日期: 20250630
2025-06-30 19:13:13 - core.http_client - INFO - 获取股票数据: 600519, 日期: 20250630
2025-06-30 19:13:13 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:13:13 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '000858'}
2025-06-30 19:13:13 - core.http_client - DEBUG - 股票 000858 数据获取成功
2025-06-30 19:13:13 - core.data_collector - DEBUG - 股票 000858 数据保存成功
2025-06-30 19:13:13 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:13:13 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:13:13 - core.http_client - INFO - 获取股票数据: 000858, 日期: 20250630
2025-06-30 19:13:13 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:13:13 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '002415'}
2025-06-30 19:13:13 - core.http_client - DEBUG - 股票 002415 数据获取成功
2025-06-30 19:13:13 - core.data_collector - DEBUG - 股票 002415 数据保存成功
2025-06-30 19:13:13 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:13:13 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:13:13 - core.http_client - INFO - 获取股票数据: 002594, 日期: 20250630
2025-06-30 19:13:13 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:13:13 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '600000'}
2025-06-30 19:13:13 - core.http_client - DEBUG - 股票 600000 数据获取成功
2025-06-30 19:13:13 - core.data_collector - DEBUG - 股票 600000 数据保存成功
2025-06-30 19:13:13 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:13:13 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:13:13 - core.http_client - INFO - 获取股票数据: 300059, 日期: 20250630
2025-06-30 19:13:14 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:13:14 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '600036'}
2025-06-30 19:13:14 - core.http_client - DEBUG - 股票 600036 数据获取成功
2025-06-30 19:13:14 - core.data_collector - DEBUG - 股票 600036 数据保存成功
2025-06-30 19:13:14 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:13:14 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:13:14 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '600519'}
2025-06-30 19:13:14 - core.http_client - DEBUG - 股票 600519 数据获取成功
2025-06-30 19:13:14 - core.data_collector - DEBUG - 股票 600519 数据保存成功
2025-06-30 19:13:14 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:13:14 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:13:14 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '000858'}
2025-06-30 19:13:14 - core.http_client - DEBUG - 股票 000858 数据获取成功
2025-06-30 19:13:14 - core.data_collector - DEBUG - 股票 000858 数据保存成功
2025-06-30 19:13:14 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:13:14 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:13:14 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '002594'}
2025-06-30 19:13:14 - core.http_client - DEBUG - 股票 002594 数据获取成功
2025-06-30 19:13:14 - core.data_collector - DEBUG - 股票 002594 数据保存成功
2025-06-30 19:13:14 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:13:14 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:13:14 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '300059'}
2025-06-30 19:13:14 - core.http_client - DEBUG - 股票 300059 数据获取成功
2025-06-30 19:13:14 - core.data_collector - DEBUG - 股票 300059 数据保存成功
2025-06-30 19:13:14 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:13:14 - core.data_collector - INFO - 批量采集完成: 成功 10, 失败 0
2025-06-30 19:13:14 - utils.logger - INFO - 函数 batch_collect_stocks 执行成功
2025-06-30 19:13:14 - __main__ - INFO - 股票数据采集结果: {'success': 10, 'failed': 0, 'total': 10}
2025-06-30 19:13:14 - utils.logger - INFO - 调用函数: collect_plate_data
2025-06-30 19:13:14 - core.data_collector - INFO - 开始采集板块数据，日期: 20250630
2025-06-30 19:13:14 - core.http_client - INFO - 获取板块数据，日期: 20250630
2025-06-30 19:13:15 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:13:15 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetPlateList', 'Day': '20250630', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886'}
2025-06-30 19:13:15 - core.http_client - DEBUG - 板块数据获取成功
2025-06-30 19:13:15 - core.data_collector - INFO - 板块数据保存成功
2025-06-30 19:13:15 - utils.logger - INFO - 函数 collect_plate_data 执行成功
2025-06-30 19:13:15 - __main__ - INFO - 板块数据采集: 成功
2025-06-30 19:13:15 - utils.logger - INFO - 调用函数: collect_longhubang_data
2025-06-30 19:13:15 - core.data_collector - INFO - 开始采集龙虎榜数据，日期: 20250630
2025-06-30 19:13:15 - core.http_client - INFO - 获取龙虎榜数据，日期: 20250630
2025-06-30 19:13:17 - utils.logger - INFO - API请求成功 - URL: https://pclhb.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:13:17 - utils.logger - DEBUG - 请求参数: {'c': 'LongHuBang', 'a': 'GetLongHuBangData', 'Day': '20250630', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886'}
2025-06-30 19:13:17 - core.http_client - DEBUG - 龙虎榜数据获取成功
2025-06-30 19:13:17 - core.data_collector - INFO - 龙虎榜数据保存成功
2025-06-30 19:13:17 - utils.logger - INFO - 函数 collect_longhubang_data 执行成功
2025-06-30 19:13:17 - __main__ - INFO - 龙虎榜数据采集: 成功
2025-06-30 19:13:17 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 19:13:28 - __main__ - INFO - === 系统状态 ===
2025-06-30 19:13:28 - __main__ - INFO - 数据库URL: sqlite:///kaipanla_data.db
2025-06-30 19:13:28 - __main__ - INFO - 日志级别: DEBUG
2025-06-30 19:13:28 - __main__ - INFO - 错误统计: {'error_count': 0, 'warning_count': 0, 'errors': [], 'warnings': []}
2025-06-30 19:13:28 - utils.logger - INFO - 调用函数: get_data_statistics
2025-06-30 19:13:28 - core.data_processor - INFO - 数据统计完成: {'stock_quote_count': 0, 'stock_tag_count': 0, 'stock_concept_count': 0, 'plate_info_count': 0, 'long_hu_bang_count': 0}
2025-06-30 19:13:28 - utils.logger - INFO - 函数 get_data_statistics 执行成功
2025-06-30 19:13:29 - __main__ - INFO - 数据统计: {'stock_quote_count': 0, 'stock_tag_count': 0, 'stock_concept_count': 0, 'plate_info_count': 0, 'long_hu_bang_count': 0}
2025-06-30 19:13:54 - __main__ - INFO - 开始导出数据: 20250630
2025-06-30 19:13:54 - utils.logger - INFO - 调用函数: export_to_excel
2025-06-30 19:13:54 - core.data_processor - INFO - 开始导出数据到Excel: test_export.xlsx
2025-06-30 19:13:55 - core.data_processor - ERROR - 导出数据到Excel失败: At least one sheet must be visible
2025-06-30 19:13:55 - utils.logger - ERROR - 函数 export_to_excel 执行失败: At least one sheet must be visible
2025-06-30 19:13:55 - __main__ - ERROR - 数据导出失败: At least one sheet must be visible
2025-06-30 19:17:19 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 19:17:20 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:17:20 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '000001'}
2025-06-30 19:17:20 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 19:17:20 - core.http_client - INFO - 获取板块数据，日期: 20250630
2025-06-30 19:17:21 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:17:21 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetPlateList', 'Day': '20250630', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886'}
2025-06-30 19:17:21 - core.http_client - DEBUG - 板块数据获取成功
2025-06-30 19:17:21 - core.http_client - INFO - 获取龙虎榜数据，日期: 20250630
2025-06-30 19:17:23 - utils.logger - INFO - API请求成功 - URL: https://pclhb.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:17:23 - utils.logger - DEBUG - 请求参数: {'c': 'LongHuBang', 'a': 'GetLongHuBangData', 'Day': '20250630', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886'}
2025-06-30 19:17:23 - core.http_client - DEBUG - 龙虎榜数据获取成功
2025-06-30 19:17:23 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 19:29:41 - core.login_manager - WARNING - 凭据已失效
2025-06-30 19:29:41 - core.login_manager - INFO - 登录管理器会话已关闭
2025-06-30 19:35:44 - core.login_manager - INFO - 凭据测试成功
2025-06-30 19:35:44 - core.login_manager - INFO - 配置文件更新成功
2025-06-30 19:35:44 - core.login_manager - INFO - 凭据测试成功
2025-06-30 19:35:44 - core.login_manager - INFO - 登录管理器会话已关闭
2025-06-30 19:35:44 - core.login_manager - INFO - 登录管理器会话已关闭
2025-06-30 19:35:55 - __main__ - INFO - 开始生成并保存模拟数据，日期: 20250630
2025-06-30 19:35:55 - __main__ - INFO - 生成股票行情数据...
2025-06-30 19:35:55 - __main__ - ERROR - 保存模拟数据失败: 'name' is an invalid keyword argument for StockQuote
2025-06-30 19:36:38 - __main__ - INFO - 开始生成并保存模拟数据，日期: 20250630
2025-06-30 19:36:38 - __main__ - INFO - 生成股票行情数据...
2025-06-30 19:36:38 - __main__ - INFO - 生成板块数据...
2025-06-30 19:36:38 - __main__ - ERROR - 保存模拟数据失败: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(sqlite3.IntegrityError) NOT NULL constraint failed: plate_info.plate_code
[SQL: INSERT INTO plate_info (plate_code, plate_name, plate_type, trade_date, change_rate, stock_count, up_count, down_count, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: (None, '沪深300', None, '20250630', 4.48, 24, 43, 40, '2025-06-30 19:36:38.148571')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-30 19:37:48 - __main__ - INFO - 开始生成并保存模拟数据，日期: 20250630
2025-06-30 19:37:48 - __main__ - INFO - 生成股票行情数据...
2025-06-30 19:37:48 - __main__ - INFO - 生成板块数据...
2025-06-30 19:37:48 - __main__ - INFO - 生成龙虎榜数据...
2025-06-30 19:37:48 - __main__ - INFO - 模拟数据保存成功
2025-06-30 19:38:06 - __main__ - INFO - === 系统状态 ===
2025-06-30 19:38:06 - __main__ - INFO - 数据库URL: sqlite:///kaipanla_data.db
2025-06-30 19:38:06 - __main__ - INFO - 日志级别: DEBUG
2025-06-30 19:38:06 - __main__ - INFO - 错误统计: {'error_count': 0, 'warning_count': 0, 'errors': [], 'warnings': []}
2025-06-30 19:38:06 - utils.logger - INFO - 调用函数: get_data_statistics
2025-06-30 19:38:06 - core.data_processor - INFO - 数据统计完成: {'stock_quote_count': 14, 'stock_tag_count': 11, 'stock_concept_count': 53, 'plate_info_count': 10, 'long_hu_bang_count': 5}
2025-06-30 19:38:06 - utils.logger - INFO - 函数 get_data_statistics 执行成功
2025-06-30 19:38:06 - __main__ - INFO - 数据统计: {'stock_quote_count': 14, 'stock_tag_count': 11, 'stock_concept_count': 53, 'plate_info_count': 10, 'long_hu_bang_count': 5}
2025-06-30 19:38:50 - __main__ - INFO - 开始导出数据: 20250630
2025-06-30 19:38:50 - utils.logger - INFO - 调用函数: export_to_excel
2025-06-30 19:38:50 - core.data_processor - INFO - 开始导出数据到Excel: demo_data.xlsx
2025-06-30 19:38:51 - core.data_processor - INFO - 数据导出完成: demo_data.xlsx
2025-06-30 19:38:51 - utils.logger - INFO - 函数 export_to_excel 执行成功
2025-06-30 19:38:51 - __main__ - INFO - 数据导出成功: demo_data.xlsx
2025-06-30 19:38:51 - utils.logger - INFO - 调用函数: get_data_statistics
2025-06-30 19:38:51 - core.data_processor - INFO - 数据统计完成: {'stock_quote_count': 14, 'stock_tag_count': 11, 'stock_concept_count': 53, 'plate_info_count': 10, 'long_hu_bang_count': 5}
2025-06-30 19:38:51 - utils.logger - INFO - 函数 get_data_statistics 执行成功
2025-06-30 19:38:51 - __main__ - INFO - 数据统计: {'stock_quote_count': 14, 'stock_tag_count': 11, 'stock_concept_count': 53, 'plate_info_count': 10, 'long_hu_bang_count': 5}
2025-06-30 19:41:10 - core.login_manager - INFO - 凭据测试成功
2025-06-30 19:41:10 - core.login_manager - INFO - 登录管理器会话已关闭
2025-06-30 19:41:22 - __main__ - INFO - 开始采集今日数据: 20250630
2025-06-30 19:41:22 - utils.logger - INFO - 调用函数: get_stock_list_from_tushare
2025-06-30 19:41:22 - core.data_collector - INFO - 开始从Tushare获取股票列表
2025-06-30 19:41:22 - core.data_collector - INFO - 获取到 10 只股票
2025-06-30 19:41:22 - utils.logger - INFO - 函数 get_stock_list_from_tushare 执行成功
2025-06-30 19:41:22 - __main__ - INFO - 获取到 10 只股票
2025-06-30 19:41:22 - utils.logger - INFO - 调用函数: batch_collect_stocks
2025-06-30 19:41:22 - core.data_collector - INFO - 开始批量采集 10 只股票数据
2025-06-30 19:41:22 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:41:22 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:41:22 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 19:41:22 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:41:22 - core.http_client - INFO - 获取股票数据: 000002, 日期: 20250630
2025-06-30 19:41:22 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:41:22 - core.http_client - INFO - 获取股票数据: 000858, 日期: 20250630
2025-06-30 19:41:22 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:41:22 - core.http_client - INFO - 获取股票数据: 002415, 日期: 20250630
2025-06-30 19:41:22 - core.http_client - INFO - 获取股票数据: 600000, 日期: 20250630
2025-06-30 19:41:23 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:41:23 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000002'}
2025-06-30 19:41:23 - core.http_client - DEBUG - 股票 000002 数据获取成功
2025-06-30 19:41:23 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:41:23 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000001'}
2025-06-30 19:41:23 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 19:41:23 - core.data_collector - ERROR - 采集股票 000001 数据失败: This session is provisioning a new connection; concurrent operations are not permitted (Background on this error at: https://sqlalche.me/e/20/isce)
2025-06-30 19:41:23 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:41:23 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:41:23 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:41:23 - core.http_client - INFO - 获取股票数据: 600036, 日期: 20250630
2025-06-30 19:41:23 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000858'}
2025-06-30 19:41:23 - core.http_client - DEBUG - 股票 000858 数据获取成功
2025-06-30 19:41:23 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:41:23 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600000'}
2025-06-30 19:41:23 - core.http_client - DEBUG - 股票 600000 数据获取成功
2025-06-30 19:41:23 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:41:23 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002415'}
2025-06-30 19:41:23 - core.http_client - DEBUG - 股票 002415 数据获取成功
2025-06-30 19:41:23 - core.data_collector - ERROR - 采集股票 002415 数据失败: Method 'rollback()' can't be called here; method 'commit()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-06-30 19:41:23 - core.data_collector - ERROR - 采集股票 600000 数据失败: Method 'rollback()' can't be called here; method 'commit()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-06-30 19:41:23 - core.data_collector - DEBUG - 股票 000002 数据保存成功
2025-06-30 19:41:23 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:41:23 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:41:23 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:41:23 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:41:23 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:41:23 - core.http_client - INFO - 获取股票数据: 600519, 日期: 20250630
2025-06-30 19:41:23 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:41:23 - core.data_collector - ERROR - 解析保存股票 000858 数据失败: This transaction is closed
2025-06-30 19:41:23 - core.data_collector - ERROR - 采集股票 000858 数据失败: This transaction is closed
2025-06-30 19:41:23 - core.http_client - INFO - 获取股票数据: 000858, 日期: 20250630
2025-06-30 19:41:23 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:41:23 - core.http_client - INFO - 获取股票数据: 002594, 日期: 20250630
2025-06-30 19:41:23 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:41:23 - core.http_client - INFO - 获取股票数据: 300059, 日期: 20250630
2025-06-30 19:41:24 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:41:24 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600036'}
2025-06-30 19:41:24 - core.http_client - DEBUG - 股票 600036 数据获取成功
2025-06-30 19:41:24 - core.data_collector - ERROR - 解析保存股票 600036 数据失败: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(sqlite3.IntegrityError) UNIQUE constraint failed: stock_concept.id
[SQL: INSERT INTO stock_concept (id, symbol, concept, trade_date, created_at) VALUES (?, ?, ?, ?, ?)]
[parameters: [(69, '000002', '超级品牌', '20250630', '2025-06-30 19:41:23.851698'), (68, '000858', '白酒', '20250630', '2025-06-30 19:41:23.851691')]]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-30 19:41:24 - core.data_collector - ERROR - 采集股票 600036 数据失败: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(sqlite3.IntegrityError) UNIQUE constraint failed: stock_concept.id
[SQL: INSERT INTO stock_concept (id, symbol, concept, trade_date, created_at) VALUES (?, ?, ?, ?, ?)]
[parameters: [(69, '000002', '超级品牌', '20250630', '2025-06-30 19:41:23.851698'), (68, '000858', '白酒', '20250630', '2025-06-30 19:41:23.851691')]]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-30 19:41:24 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:41:24 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:41:24 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600519'}
2025-06-30 19:41:24 - core.http_client - DEBUG - 股票 600519 数据获取成功
2025-06-30 19:41:24 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:41:24 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000858'}
2025-06-30 19:41:24 - core.http_client - DEBUG - 股票 000858 数据获取成功
2025-06-30 19:41:24 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:41:24 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002594'}
2025-06-30 19:41:24 - core.http_client - DEBUG - 股票 002594 数据获取成功
2025-06-30 19:41:24 - core.data_collector - ERROR - 采集股票 002594 数据失败: Method 'rollback()' can't be called here; method 'commit()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-06-30 19:41:24 - core.data_collector - ERROR - 采集股票 000858 数据失败: Method 'rollback()' can't be called here; method 'commit()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-06-30 19:41:24 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:41:24 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:41:24 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:41:24 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300059'}
2025-06-30 19:41:24 - core.http_client - DEBUG - 股票 300059 数据获取成功
2025-06-30 19:41:24 - core.data_collector - ERROR - 采集股票 300059 数据失败: Method 'rollback()' can't be called here; method 'commit()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-06-30 19:41:24 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:41:24 - core.data_collector - DEBUG - 股票 600519 数据保存成功
2025-06-30 19:41:24 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:41:24 - core.data_collector - INFO - 批量采集完成: 成功 2, 失败 8
2025-06-30 19:41:24 - utils.logger - INFO - 函数 batch_collect_stocks 执行成功
2025-06-30 19:41:24 - __main__ - INFO - 股票数据采集结果: {'success': 2, 'failed': 8, 'total': 10}
2025-06-30 19:41:24 - utils.logger - INFO - 调用函数: collect_plate_data
2025-06-30 19:41:24 - core.data_collector - INFO - 开始采集板块数据，日期: 20250630
2025-06-30 19:41:24 - core.http_client - INFO - 获取板块数据，日期: 20250630
2025-06-30 19:41:26 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:41:26 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetPlateList', 'Day': '20250630', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 19:41:26 - core.http_client - ERROR - 获取板块数据时发生错误: Expecting value: line 7 column 1 (char 6)
2025-06-30 19:41:26 - utils.logger - INFO - 函数 collect_plate_data 执行成功
2025-06-30 19:41:26 - __main__ - INFO - 板块数据采集: 失败
2025-06-30 19:41:26 - utils.logger - INFO - 调用函数: collect_longhubang_data
2025-06-30 19:41:26 - core.data_collector - INFO - 开始采集龙虎榜数据，日期: 20250630
2025-06-30 19:41:26 - core.http_client - INFO - 获取龙虎榜数据，日期: 20250630
2025-06-30 19:41:27 - utils.logger - INFO - API请求成功 - URL: https://pclhb.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:41:27 - utils.logger - DEBUG - 请求参数: {'c': 'LongHuBang', 'a': 'GetLongHuBangData', 'Day': '20250630', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 19:41:27 - core.http_client - ERROR - 获取龙虎榜数据时发生错误: Expecting value: line 1 column 1 (char 0)
2025-06-30 19:41:27 - utils.logger - INFO - 函数 collect_longhubang_data 执行成功
2025-06-30 19:41:27 - __main__ - INFO - 龙虎榜数据采集: 失败
2025-06-30 19:41:27 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 19:42:07 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 19:42:08 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:42:08 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000001'}
2025-06-30 19:42:08 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 19:42:09 - core.http_client - INFO - 获取板块数据，日期: 20250630
2025-06-30 19:42:10 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:42:10 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetPlateList', 'Day': '20250630', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 19:42:10 - core.http_client - ERROR - 获取板块数据时发生错误: Expecting value: line 7 column 1 (char 6)
2025-06-30 19:42:10 - core.http_client - INFO - 获取龙虎榜数据，日期: 20250630
2025-06-30 19:42:11 - utils.logger - INFO - API请求成功 - URL: https://pclhb.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:42:11 - utils.logger - DEBUG - 请求参数: {'c': 'LongHuBang', 'a': 'GetLongHuBangData', 'Day': '20250630', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 19:42:11 - core.http_client - ERROR - 获取龙虎榜数据时发生错误: Expecting value: line 1 column 1 (char 0)
2025-06-30 19:42:11 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 19:51:44 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:51:44 - core.http_client - INFO - 获取股票数据: ['000001'], 日期: 20250630
2025-06-30 19:51:45 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:51:45 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': ['000001']}
2025-06-30 19:51:45 - core.http_client - DEBUG - 股票 ['000001'] 数据获取成功
2025-06-30 19:51:45 - core.data_collector - ERROR - 解析保存股票 ['000001'] 数据失败: (sqlite3.ProgrammingError) Error binding parameter 1: type 'list' is not supported
[SQL: SELECT stock_quote.id AS stock_quote_id, stock_quote.symbol AS stock_quote_symbol, stock_quote.trade_date AS stock_quote_trade_date, stock_quote.current_price AS stock_quote_current_price, stock_quote.open_price AS stock_quote_open_price, stock_quote.high_price AS stock_quote_high_price, stock_quote.low_price AS stock_quote_low_price, stock_quote.close_price AS stock_quote_close_price, stock_quote.volume AS stock_quote_volume, stock_quote.amount AS stock_quote_amount, stock_quote.change_rate AS stock_quote_change_rate, stock_quote.change_amount AS stock_quote_change_amount, stock_quote.created_at AS stock_quote_created_at 
FROM stock_quote 
WHERE stock_quote.symbol = ? AND stock_quote.trade_date = ?
 LIMIT ? OFFSET ?]
[parameters: (['000001'], '20250630', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-30 19:51:45 - core.data_collector - ERROR - 采集股票 ['000001'] 数据失败: (sqlite3.ProgrammingError) Error binding parameter 1: type 'list' is not supported
[SQL: SELECT stock_quote.id AS stock_quote_id, stock_quote.symbol AS stock_quote_symbol, stock_quote.trade_date AS stock_quote_trade_date, stock_quote.current_price AS stock_quote_current_price, stock_quote.open_price AS stock_quote_open_price, stock_quote.high_price AS stock_quote_high_price, stock_quote.low_price AS stock_quote_low_price, stock_quote.close_price AS stock_quote_close_price, stock_quote.volume AS stock_quote_volume, stock_quote.amount AS stock_quote_amount, stock_quote.change_rate AS stock_quote_change_rate, stock_quote.change_amount AS stock_quote_change_amount, stock_quote.created_at AS stock_quote_created_at 
FROM stock_quote 
WHERE stock_quote.symbol = ? AND stock_quote.trade_date = ?
 LIMIT ? OFFSET ?]
[parameters: (['000001'], '20250630', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-30 19:51:45 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:51:45 - utils.logger - INFO - 调用函数: collect_plate_data
2025-06-30 19:51:45 - core.data_collector - INFO - 开始采集板块数据，日期: 20250630
2025-06-30 19:51:45 - core.http_client - INFO - 获取板块数据，日期: 20250630
2025-06-30 19:51:46 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:51:46 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetPlateList', 'Day': '20250630', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 19:51:46 - core.http_client - WARNING - 板块数据API返回空内容，可能该日期无数据
2025-06-30 19:51:46 - utils.logger - INFO - 函数 collect_plate_data 执行成功
2025-06-30 19:51:46 - utils.logger - INFO - 调用函数: collect_longhubang_data
2025-06-30 19:51:46 - core.data_collector - INFO - 开始采集龙虎榜数据，日期: 20250630
2025-06-30 19:51:46 - core.http_client - INFO - 获取龙虎榜数据，日期: 20250630
2025-06-30 19:51:48 - utils.logger - INFO - API请求成功 - URL: https://pclhb.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:51:48 - utils.logger - DEBUG - 请求参数: {'c': 'LongHuBang', 'a': 'GetLongHuBangData', 'Day': '20250630', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 19:51:48 - core.http_client - WARNING - 龙虎榜数据API返回空内容，可能该日期无数据
2025-06-30 19:51:48 - utils.logger - INFO - 函数 collect_longhubang_data 执行成功
2025-06-30 19:51:48 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 19:53:47 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:53:47 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 19:53:49 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:53:49 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000001'}
2025-06-30 19:53:49 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 19:53:49 - core.data_collector - DEBUG - 股票 000001 数据保存成功
2025-06-30 19:53:49 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:53:49 - utils.logger - INFO - 调用函数: batch_collect_stocks
2025-06-30 19:53:49 - core.data_collector - INFO - 开始批量采集 2 只股票数据
2025-06-30 19:53:49 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:53:49 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:53:49 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 19:53:49 - core.http_client - INFO - 获取股票数据: 000002, 日期: 20250630
2025-06-30 19:53:50 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:53:50 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000001'}
2025-06-30 19:53:50 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 19:53:50 - core.data_collector - DEBUG - 股票 000001 数据保存成功
2025-06-30 19:53:50 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:53:50 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:53:50 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000002'}
2025-06-30 19:53:50 - core.http_client - DEBUG - 股票 000002 数据获取成功
2025-06-30 19:53:50 - core.data_collector - DEBUG - 股票 000002 数据保存成功
2025-06-30 19:53:50 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:53:50 - core.data_collector - INFO - 批量采集完成: 成功 2, 失败 0
2025-06-30 19:53:50 - utils.logger - INFO - 函数 batch_collect_stocks 执行成功
2025-06-30 19:53:50 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 20:08:27 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 20:08:29 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:08:29 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000001'}
2025-06-30 20:08:29 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 20:08:29 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 20:09:48 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 20:09:49 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:09:49 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000001'}
2025-06-30 20:09:49 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 20:09:49 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 20:12:44 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 20:12:44 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 20:12:45 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:12:45 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000001'}
2025-06-30 20:12:45 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 20:12:45 - core.data_collector - DEBUG - 股票 000001 数据保存成功
2025-06-30 20:12:45 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 20:12:45 - utils.logger - INFO - 调用函数: collect_plate_data
2025-06-30 20:12:45 - core.data_collector - INFO - 板块数据已通过股票API获取，日期: 20250630
2025-06-30 20:12:45 - core.data_collector - INFO - 板块数据包含在每个股票的stockplate和careplate字段中
2025-06-30 20:12:45 - core.data_collector - WARNING - 独立的板块API已失效，板块数据通过股票数据收集获得
2025-06-30 20:12:45 - utils.logger - INFO - 函数 collect_plate_data 执行成功
2025-06-30 20:12:45 - utils.logger - INFO - 调用函数: collect_longhubang_data
2025-06-30 20:12:45 - core.data_collector - INFO - 龙虎榜数据API当前不可用，日期: 20250630
2025-06-30 20:12:45 - core.data_collector - WARNING - 独立的龙虎榜API返回空内容，可能已失效或需要特殊权限
2025-06-30 20:12:45 - core.data_collector - INFO - 建议通过其他数据源获取龙虎榜数据
2025-06-30 20:12:45 - utils.logger - INFO - 函数 collect_longhubang_data 执行成功
2025-06-30 20:12:45 - utils.logger - INFO - 调用函数: batch_collect_stocks
2025-06-30 20:12:45 - core.data_collector - INFO - 开始批量采集 2 只股票数据
2025-06-30 20:12:45 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 20:12:45 - core.http_client - INFO - 获取股票数据: 000002, 日期: 20250630
2025-06-30 20:12:45 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 20:12:45 - core.http_client - INFO - 获取股票数据: 000858, 日期: 20250630
2025-06-30 20:12:46 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:12:46 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000002'}
2025-06-30 20:12:46 - core.http_client - DEBUG - 股票 000002 数据获取成功
2025-06-30 20:12:46 - core.data_collector - DEBUG - 股票 000002 数据保存成功
2025-06-30 20:12:46 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 20:12:46 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:12:46 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000858'}
2025-06-30 20:12:46 - core.http_client - DEBUG - 股票 000858 数据获取成功
2025-06-30 20:12:46 - core.data_collector - DEBUG - 股票 000858 数据保存成功
2025-06-30 20:12:46 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 20:12:46 - core.data_collector - INFO - 批量采集完成: 成功 2, 失败 0
2025-06-30 20:12:46 - utils.logger - INFO - 函数 batch_collect_stocks 执行成功
2025-06-30 20:12:46 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 20:22:34 - core.http_client - INFO - 获取龙虎榜数据，日期: 20250630
2025-06-30 20:22:36 - utils.logger - INFO - API请求成功 - URL: https://pclhb.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:22:36 - utils.logger - DEBUG - 请求参数: {'c': 'LongHuBang', 'a': 'GetStockList', 'Index': '0', 'st': '300', 'Time': '', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 20:22:36 - core.http_client - DEBUG - 龙虎榜数据获取成功
2025-06-30 20:22:36 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 20:32:53 - core.http_client - INFO - 获取板块数据，日期: 2025-06-30
2025-06-30 20:32:54 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:32:54 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'Date': '2025-06-30', 'PType': '2', 'POrder': '1', 'ZSType': '5', 'rate': '1', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '20', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 20:32:54 - core.http_client - DEBUG - 板块数据获取成功
2025-06-30 20:32:54 - core.http_client - INFO - 获取板块数据，日期: 20250630
2025-06-30 20:32:55 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:32:55 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'Date': '20250630', 'PType': '2', 'POrder': '1', 'ZSType': '5', 'rate': '1', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '20', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 20:32:55 - core.http_client - DEBUG - 板块数据获取成功
2025-06-30 20:32:55 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 20:34:17 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 20:34:18 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:34:18 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000001'}
2025-06-30 20:34:18 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 20:34:18 - core.http_client - INFO - 获取板块数据，日期: 2025-06-30
2025-06-30 20:34:19 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:34:19 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'Date': '2025-06-30', 'PType': '2', 'POrder': '1', 'ZSType': '5', 'rate': '1', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '20', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 20:34:19 - core.http_client - DEBUG - 板块数据获取成功
2025-06-30 20:34:19 - core.http_client - INFO - 获取核心板块数据，ZSType: 7
2025-06-30 20:34:20 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:34:20 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '17', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 20:34:20 - core.http_client - DEBUG - 核心板块数据获取成功
2025-06-30 20:34:20 - core.http_client - INFO - 获取龙虎榜数据，日期: 20250630
2025-06-30 20:34:22 - utils.logger - INFO - API请求成功 - URL: https://pclhb.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:34:22 - utils.logger - DEBUG - 请求参数: {'c': 'LongHuBang', 'a': 'GetStockList', 'Index': '0', 'st': '300', 'Time': '', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 20:34:22 - core.http_client - DEBUG - 龙虎榜数据获取成功
2025-06-30 20:34:22 - core.http_client - INFO - 获取新闻数据，索引: 0, 数量: 8
2025-06-30 20:34:23 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:34:23 - utils.logger - DEBUG - 请求参数: {'c': 'PCNewsFlash', 'a': 'GetList', 'Index': '0', 'st': '8', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 20:34:23 - core.http_client - WARNING - 新闻数据API返回空内容
2025-06-30 20:34:23 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 20:35:49 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 20:35:49 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 20:35:51 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:35:51 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000001'}
2025-06-30 20:35:51 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 20:35:51 - core.data_collector - DEBUG - 股票 000001 数据保存成功
2025-06-30 20:35:51 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 20:35:51 - utils.logger - INFO - 调用函数: collect_plate_data
2025-06-30 20:35:51 - core.data_collector - INFO - 开始采集板块数据，日期: 20250630
2025-06-30 20:35:51 - core.http_client - INFO - 获取板块数据，日期: 2025-06-30
2025-06-30 20:35:52 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:35:52 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'Date': '2025-06-30', 'PType': '2', 'POrder': '1', 'ZSType': '5', 'rate': '1', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '20', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 20:35:52 - core.http_client - DEBUG - 板块数据获取成功
2025-06-30 20:35:52 - core.data_collector - ERROR - 解析保存板块数据失败: 'str' object has no attribute 'get'
2025-06-30 20:35:52 - core.data_collector - ERROR - 采集板块数据失败: 'str' object has no attribute 'get'
2025-06-30 20:35:52 - utils.logger - INFO - 函数 collect_plate_data 执行成功
2025-06-30 20:35:52 - utils.logger - INFO - 调用函数: collect_longhubang_data
2025-06-30 20:35:52 - core.data_collector - INFO - 开始采集龙虎榜数据，日期: 20250630
2025-06-30 20:35:52 - core.http_client - INFO - 获取龙虎榜数据，日期: 20250630
2025-06-30 20:35:53 - utils.logger - INFO - API请求成功 - URL: https://pclhb.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:35:53 - utils.logger - DEBUG - 请求参数: {'c': 'LongHuBang', 'a': 'GetStockList', 'Index': '0', 'st': '300', 'Time': '', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 20:35:53 - core.http_client - DEBUG - 龙虎榜数据获取成功
2025-06-30 20:35:53 - core.data_collector - INFO - 龙虎榜数据保存成功
2025-06-30 20:35:53 - utils.logger - INFO - 函数 collect_longhubang_data 执行成功
2025-06-30 20:35:53 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 20:36:16 - core.http_client - INFO - 获取板块数据，日期: 2025-06-30
2025-06-30 20:36:17 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:36:17 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'Date': '2025-06-30', 'PType': '2', 'POrder': '1', 'ZSType': '5', 'rate': '1', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '20', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 20:36:17 - core.http_client - DEBUG - 板块数据获取成功
2025-06-30 20:36:17 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 20:38:19 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 20:38:19 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 20:38:20 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:38:20 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000001'}
2025-06-30 20:38:20 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 20:38:20 - core.data_collector - DEBUG - 股票 000001 数据保存成功
2025-06-30 20:38:20 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 20:38:20 - utils.logger - INFO - 调用函数: collect_plate_data
2025-06-30 20:38:20 - core.data_collector - INFO - 开始采集板块数据，日期: 20250630
2025-06-30 20:38:20 - core.http_client - INFO - 获取板块数据，日期: 2025-06-30
2025-06-30 20:38:21 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:38:21 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'Date': '2025-06-30', 'PType': '2', 'POrder': '1', 'ZSType': '5', 'rate': '1', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '20', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 20:38:21 - core.http_client - DEBUG - 板块数据获取成功
2025-06-30 20:38:21 - core.data_collector - INFO - 解析了 20 个板块数据
2025-06-30 20:38:21 - core.data_collector - INFO - 板块数据保存成功
2025-06-30 20:38:21 - utils.logger - INFO - 函数 collect_plate_data 执行成功
2025-06-30 20:38:21 - utils.logger - INFO - 调用函数: collect_longhubang_data
2025-06-30 20:38:21 - core.data_collector - INFO - 开始采集龙虎榜数据，日期: 20250630
2025-06-30 20:38:21 - core.http_client - INFO - 获取龙虎榜数据，日期: 20250630
2025-06-30 20:38:22 - utils.logger - INFO - API请求成功 - URL: https://pclhb.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:38:22 - utils.logger - DEBUG - 请求参数: {'c': 'LongHuBang', 'a': 'GetStockList', 'Index': '0', 'st': '300', 'Time': '', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 20:38:22 - core.http_client - DEBUG - 龙虎榜数据获取成功
2025-06-30 20:38:22 - core.data_collector - INFO - 龙虎榜数据保存成功
2025-06-30 20:38:22 - utils.logger - INFO - 函数 collect_longhubang_data 执行成功
2025-06-30 20:38:22 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 21:50:16 - core.http_client - INFO - 获取核心板块数据，ZSType: 7
2025-06-30 21:50:17 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 21:50:17 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '17', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 21:50:17 - core.http_client - DEBUG - 核心板块数据获取成功
2025-06-30 21:50:17 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 21:51:08 - core.http_client - INFO - 获取核心板块数据，ZSType: 7
2025-06-30 21:51:09 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 21:51:09 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '17', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 21:51:09 - core.http_client - DEBUG - 核心板块数据获取成功
2025-06-30 21:51:09 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 21:59:34 - utils.logger - INFO - 调用函数: collect_plate_strength_data
2025-06-30 21:59:34 - core.data_collector - INFO - 开始采集板块强度数据，日期: 20250630
2025-06-30 21:59:34 - core.http_client - INFO - 获取板块强度数据，索引: 0, 数量: 17
2025-06-30 21:59:35 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 21:59:35 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '17', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 21:59:35 - core.http_client - DEBUG - 板块强度数据获取成功
2025-06-30 21:59:35 - core.data_collector - ERROR - 采集板块强度数据失败: name 'create_engine' is not defined
2025-06-30 21:59:35 - utils.logger - INFO - 函数 collect_plate_strength_data 执行成功
2025-06-30 21:59:35 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 22:00:07 - utils.logger - INFO - 调用函数: collect_plate_strength_data
2025-06-30 22:00:07 - core.data_collector - INFO - 开始采集板块强度数据，日期: 20250630
2025-06-30 22:00:07 - core.http_client - INFO - 获取板块强度数据，索引: 0, 数量: 17
2025-06-30 22:00:08 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:00:08 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '17', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 22:00:08 - core.http_client - DEBUG - 板块强度数据获取成功
2025-06-30 22:00:08 - core.data_collector - ERROR - 采集板块强度数据失败: name 'Config' is not defined
2025-06-30 22:00:08 - utils.logger - INFO - 函数 collect_plate_strength_data 执行成功
2025-06-30 22:00:08 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 22:00:31 - utils.logger - INFO - 调用函数: collect_plate_strength_data
2025-06-30 22:00:31 - core.data_collector - INFO - 开始采集板块强度数据，日期: 20250630
2025-06-30 22:00:31 - core.http_client - INFO - 获取板块强度数据，索引: 0, 数量: 17
2025-06-30 22:00:32 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:00:32 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '17', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 22:00:32 - core.http_client - DEBUG - 板块强度数据获取成功
2025-06-30 22:00:32 - core.data_collector - ERROR - 采集板块强度数据失败: name 'Config' is not defined
2025-06-30 22:00:32 - utils.logger - INFO - 函数 collect_plate_strength_data 执行成功
2025-06-30 22:00:32 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 22:01:05 - utils.logger - INFO - 调用函数: collect_plate_strength_data
2025-06-30 22:01:05 - core.data_collector - INFO - 开始采集板块强度数据，日期: 20250630
2025-06-30 22:01:05 - core.http_client - INFO - 获取板块强度数据，索引: 0, 数量: 17
2025-06-30 22:01:06 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:01:06 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '17', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 22:01:06 - core.http_client - DEBUG - 板块强度数据获取成功
2025-06-30 22:01:06 - core.data_collector - INFO - 板块强度数据保存完成，新增 17 条记录
2025-06-30 22:01:06 - core.data_collector - INFO - 板块强度数据采集完成，日期: 20250630
2025-06-30 22:01:06 - utils.logger - INFO - 函数 collect_plate_strength_data 执行成功
2025-06-30 22:01:06 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 22:04:09 - core.http_client - INFO - 获取板块强度数据，索引: 0, 数量: 17
2025-06-30 22:04:10 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:04:10 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '17', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 22:04:10 - core.http_client - DEBUG - 板块强度数据获取成功
2025-06-30 22:04:10 - core.http_client - INFO - 获取板块强度数据，索引: 0, 数量: 50
2025-06-30 22:04:11 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:04:11 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '50', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 22:04:11 - core.http_client - DEBUG - 板块强度数据获取成功
2025-06-30 22:04:11 - core.http_client - INFO - 获取板块强度数据，索引: 0, 数量: 100
2025-06-30 22:04:12 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:04:12 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '100', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 22:04:12 - core.http_client - DEBUG - 板块强度数据获取成功
2025-06-30 22:04:12 - core.http_client - INFO - 获取板块强度数据，索引: 0, 数量: 200
2025-06-30 22:04:13 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:04:13 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '200', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 22:04:13 - core.http_client - DEBUG - 板块强度数据获取成功
2025-06-30 22:04:13 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 22:06:22 - utils.logger - INFO - 调用函数: collect_plate_strength_data
2025-06-30 22:06:22 - core.data_collector - INFO - 开始采集板块强度数据，日期: 20250630
2025-06-30 22:06:22 - core.http_client - INFO - 获取板块强度数据，索引: 0, 数量: 300
2025-06-30 22:06:24 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:06:24 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '300', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 22:06:24 - core.http_client - DEBUG - 板块强度数据获取成功
2025-06-30 22:06:24 - core.data_collector - INFO - 板块强度数据保存完成，新增 255 条记录
2025-06-30 22:06:24 - core.data_collector - INFO - 板块强度数据采集完成，日期: 20250630
2025-06-30 22:06:24 - utils.logger - INFO - 函数 collect_plate_strength_data 执行成功
2025-06-30 22:06:24 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 22:10:40 - core.http_client - INFO - 获取核心板块数据，ZSType: 7
2025-06-30 22:10:42 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:10:42 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '300', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 22:10:42 - core.http_client - DEBUG - 核心板块数据获取成功
2025-06-30 22:10:42 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 22:11:59 - utils.logger - INFO - 调用函数: collect_plate_data
2025-06-30 22:11:59 - core.data_collector - INFO - 开始采集板块数据，日期: 20250630
2025-06-30 22:11:59 - core.http_client - INFO - 获取板块数据，日期: 2025-06-30
2025-06-30 22:12:00 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:00 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'Date': '2025-06-30', 'PType': '2', 'POrder': '1', 'ZSType': '5', 'rate': '1', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '20', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 22:12:00 - core.http_client - DEBUG - 板块数据获取成功
2025-06-30 22:12:00 - core.data_collector - INFO - 解析了 20 个板块数据
2025-06-30 22:12:00 - core.data_collector - INFO - 板块数据保存成功
2025-06-30 22:12:00 - utils.logger - INFO - 函数 collect_plate_data 执行成功
2025-06-30 22:12:00 - utils.logger - INFO - 调用函数: collect_plate_strength_data
2025-06-30 22:12:00 - core.data_collector - INFO - 开始采集板块强度数据，日期: 20250630
2025-06-30 22:12:00 - core.http_client - INFO - 获取板块强度数据，索引: 0, 数量: 300
2025-06-30 22:12:01 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:01 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '300', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 22:12:01 - core.http_client - DEBUG - 板块强度数据获取成功
2025-06-30 22:12:02 - core.data_collector - INFO - 板块强度数据保存完成，新增 255 条记录
2025-06-30 22:12:02 - core.data_collector - INFO - 板块强度数据采集完成，日期: 20250630
2025-06-30 22:12:02 - utils.logger - INFO - 函数 collect_plate_strength_data 执行成功
2025-06-30 22:12:02 - utils.logger - INFO - 调用函数: collect_longhubang_data
2025-06-30 22:12:02 - core.data_collector - INFO - 开始采集龙虎榜数据，日期: 20250630
2025-06-30 22:12:02 - core.http_client - INFO - 获取龙虎榜数据，日期: 20250630
2025-06-30 22:12:03 - utils.logger - INFO - API请求成功 - URL: https://pclhb.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:03 - utils.logger - DEBUG - 请求参数: {'c': 'LongHuBang', 'a': 'GetStockList', 'Index': '0', 'st': '300', 'Time': '', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 22:12:03 - core.http_client - DEBUG - 龙虎榜数据获取成功
2025-06-30 22:12:03 - core.data_collector - INFO - 龙虎榜数据保存成功
2025-06-30 22:12:03 - utils.logger - INFO - 函数 collect_longhubang_data 执行成功
2025-06-30 22:12:03 - utils.logger - INFO - 调用函数: batch_collect_stocks
2025-06-30 22:12:03 - core.data_collector - INFO - 开始批量采集 15 只股票数据
2025-06-30 22:12:03 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:03 - core.http_client - INFO - 获取股票数据: 600887, 日期: 20250630
2025-06-30 22:12:03 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:03 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:03 - core.http_client - INFO - 获取股票数据: 300015, 日期: 20250630
2025-06-30 22:12:03 - core.http_client - INFO - 获取股票数据: 002304, 日期: 20250630
2025-06-30 22:12:04 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:04 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600887'}
2025-06-30 22:12:04 - core.http_client - DEBUG - 股票 600887 数据获取成功
2025-06-30 22:12:04 - core.data_collector - DEBUG - 股票 600887 数据保存成功
2025-06-30 22:12:04 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:04 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:04 - core.http_client - INFO - 获取股票数据: 000725, 日期: 20250630
2025-06-30 22:12:04 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:04 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300015'}
2025-06-30 22:12:04 - core.http_client - DEBUG - 股票 300015 数据获取成功
2025-06-30 22:12:04 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:04 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002304'}
2025-06-30 22:12:04 - core.http_client - DEBUG - 股票 002304 数据获取成功
2025-06-30 22:12:04 - core.data_collector - DEBUG - 股票 300015 数据保存成功
2025-06-30 22:12:04 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:04 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:04 - core.http_client - INFO - 获取股票数据: 600276, 日期: 20250630
2025-06-30 22:12:04 - core.data_collector - DEBUG - 股票 002304 数据保存成功
2025-06-30 22:12:04 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:04 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:04 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 22:12:05 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:05 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000725'}
2025-06-30 22:12:05 - core.http_client - DEBUG - 股票 000725 数据获取成功
2025-06-30 22:12:05 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:05 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600276'}
2025-06-30 22:12:05 - core.http_client - DEBUG - 股票 600276 数据获取成功
2025-06-30 22:12:05 - core.data_collector - DEBUG - 股票 000725 数据保存成功
2025-06-30 22:12:05 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:05 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:05 - core.http_client - INFO - 获取股票数据: 000002, 日期: 20250630
2025-06-30 22:12:05 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:05 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000001'}
2025-06-30 22:12:05 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 22:12:06 - core.data_collector - DEBUG - 股票 600276 数据保存成功
2025-06-30 22:12:06 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:06 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:06 - core.http_client - INFO - 获取股票数据: 600000, 日期: 20250630
2025-06-30 22:12:06 - core.data_collector - DEBUG - 股票 000001 数据保存成功
2025-06-30 22:12:06 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:06 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:06 - core.http_client - INFO - 获取股票数据: 600036, 日期: 20250630
2025-06-30 22:12:07 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:07 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000002'}
2025-06-30 22:12:07 - core.http_client - DEBUG - 股票 000002 数据获取成功
2025-06-30 22:12:07 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:07 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600000'}
2025-06-30 22:12:07 - core.http_client - DEBUG - 股票 600000 数据获取成功
2025-06-30 22:12:07 - core.data_collector - DEBUG - 股票 000002 数据保存成功
2025-06-30 22:12:07 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:07 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:07 - core.http_client - INFO - 获取股票数据: 000858, 日期: 20250630
2025-06-30 22:12:07 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:07 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600036'}
2025-06-30 22:12:07 - core.http_client - DEBUG - 股票 600036 数据获取成功
2025-06-30 22:12:07 - core.data_collector - DEBUG - 股票 600000 数据保存成功
2025-06-30 22:12:07 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:07 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:07 - core.http_client - INFO - 获取股票数据: 002415, 日期: 20250630
2025-06-30 22:12:07 - core.data_collector - DEBUG - 股票 600036 数据保存成功
2025-06-30 22:12:07 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:07 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:07 - core.http_client - INFO - 获取股票数据: 002594, 日期: 20250630
2025-06-30 22:12:08 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:08 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000858'}
2025-06-30 22:12:08 - core.http_client - DEBUG - 股票 000858 数据获取成功
2025-06-30 22:12:08 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:08 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002415'}
2025-06-30 22:12:08 - core.http_client - DEBUG - 股票 002415 数据获取成功
2025-06-30 22:12:08 - core.data_collector - DEBUG - 股票 000858 数据保存成功
2025-06-30 22:12:08 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:08 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:08 - core.http_client - INFO - 获取股票数据: 300059, 日期: 20250630
2025-06-30 22:12:08 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:08 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002594'}
2025-06-30 22:12:08 - core.http_client - DEBUG - 股票 002594 数据获取成功
2025-06-30 22:12:08 - core.data_collector - DEBUG - 股票 002415 数据保存成功
2025-06-30 22:12:08 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:08 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:08 - core.http_client - INFO - 获取股票数据: 600519, 日期: 20250630
2025-06-30 22:12:08 - core.data_collector - DEBUG - 股票 002594 数据保存成功
2025-06-30 22:12:08 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:08 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:08 - core.http_client - INFO - 获取股票数据: 000002, 日期: 20250630
2025-06-30 22:12:09 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:09 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300059'}
2025-06-30 22:12:09 - core.http_client - DEBUG - 股票 300059 数据获取成功
2025-06-30 22:12:09 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:09 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600519'}
2025-06-30 22:12:09 - core.http_client - DEBUG - 股票 600519 数据获取成功
2025-06-30 22:12:09 - core.data_collector - DEBUG - 股票 300059 数据保存成功
2025-06-30 22:12:09 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:09 - core.data_collector - DEBUG - 股票 600519 数据保存成功
2025-06-30 22:12:09 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:09 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:09 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000002'}
2025-06-30 22:12:09 - core.http_client - DEBUG - 股票 000002 数据获取成功
2025-06-30 22:12:09 - core.data_collector - DEBUG - 股票 000002 数据保存成功
2025-06-30 22:12:09 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:09 - core.data_collector - INFO - 批量采集完成: 成功 15, 失败 0
2025-06-30 22:12:09 - utils.logger - INFO - 函数 batch_collect_stocks 执行成功
2025-06-30 22:12:09 - core.http_client - INFO - HTTP会话已关闭
2025-07-01 09:40:01 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 09:40:01 - utils.logger - DEBUG - 请求参数: {'c': 'StockList', 'a': 'GetAllStocks', 'UserID': '710743', 'Token': '********************************'}
2025-07-01 09:40:02 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 09:40:02 - utils.logger - DEBUG - 请求参数: {'c': 'Stock', 'a': 'GetStockList', 'UserID': '710743', 'Token': '********************************'}
2025-07-01 09:40:03 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 09:40:03 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetStockList', 'UserID': '710743', 'Token': '********************************'}
2025-07-01 09:40:04 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 09:40:04 - utils.logger - DEBUG - 请求参数: {'c': 'StockData', 'a': 'GetList', 'UserID': '710743', 'Token': '********************************'}
2025-07-01 09:40:05 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 09:40:05 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexStock', 'SelType': '1', 'ZSType': '1', 'PType': '1', 'POrder': '0', 'PIndex': '0', 'Pst': '100', 'UserID': '710743', 'Token': '********************************'}
2025-07-01 09:40:06 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 09:40:06 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexStock', 'SelType': '2', 'ZSType': '1', 'PType': '1', 'POrder': '0', 'PIndex': '0', 'Pst': '300', 'UserID': '710743', 'Token': '********************************'}
2025-07-01 09:40:07 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 09:40:07 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexStock', 'SelType': '2', 'ZSType': '2', 'PType': '1', 'POrder': '0', 'PIndex': '0', 'Pst': '300', 'UserID': '710743', 'Token': '********************************'}
2025-07-01 09:40:08 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 09:40:08 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexStock', 'SelType': '2', 'ZSType': '3', 'PType': '1', 'POrder': '0', 'PIndex': '0', 'Pst': '300', 'UserID': '710743', 'Token': '********************************'}
2025-07-01 09:40:09 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 09:40:09 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetStockArrange', 'SelType': '2', 'ZSType': '1', 'PType': '1', 'POrder': '0', 'PIndex': '0', 'Pst': '300', 'UserID': '710743', 'Token': '********************************'}
2025-07-01 09:40:10 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 09:40:10 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetAllStock', 'SelType': '2', 'ZSType': '1', 'PType': '1', 'POrder': '0', 'PIndex': '0', 'Pst': '300', 'UserID': '710743', 'Token': '********************************'}
2025-07-01 09:40:10 - core.http_client - INFO - HTTP会话已关闭
2025-07-01 10:16:25 - core.http_client - INFO - 获取股票数据: 300911, 日期: 20250630
2025-07-01 10:16:26 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:26 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300911'}
2025-07-01 10:16:26 - core.http_client - DEBUG - 股票 300911 数据获取成功
2025-07-01 10:16:26 - core.http_client - INFO - 获取股票数据: 301942, 日期: 20250630
2025-07-01 10:16:27 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:27 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301942'}
2025-07-01 10:16:27 - core.http_client - DEBUG - 股票 301942 数据获取成功
2025-07-01 10:16:27 - core.http_client - INFO - 获取股票数据: 601840, 日期: 20250630
2025-07-01 10:16:28 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:28 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601840'}
2025-07-01 10:16:28 - core.http_client - DEBUG - 股票 601840 数据获取成功
2025-07-01 10:16:28 - core.http_client - INFO - 获取股票数据: 603095, 日期: 20250630
2025-07-01 10:16:29 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:29 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '603095'}
2025-07-01 10:16:29 - core.http_client - DEBUG - 股票 603095 数据获取成功
2025-07-01 10:16:30 - core.http_client - INFO - 获取股票数据: 601163, 日期: 20250630
2025-07-01 10:16:31 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:31 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601163'}
2025-07-01 10:16:31 - core.http_client - DEBUG - 股票 601163 数据获取成功
2025-07-01 10:16:31 - core.http_client - INFO - 获取股票数据: 603856, 日期: 20250630
2025-07-01 10:16:32 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:32 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '603856'}
2025-07-01 10:16:32 - core.http_client - DEBUG - 股票 603856 数据获取成功
2025-07-01 10:16:32 - core.http_client - INFO - 获取股票数据: 301889, 日期: 20250630
2025-07-01 10:16:33 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:33 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301889'}
2025-07-01 10:16:33 - core.http_client - DEBUG - 股票 301889 数据获取成功
2025-07-01 10:16:33 - core.http_client - INFO - 获取股票数据: 603578, 日期: 20250630
2025-07-01 10:16:34 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:34 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '603578'}
2025-07-01 10:16:34 - core.http_client - DEBUG - 股票 603578 数据获取成功
2025-07-01 10:16:34 - core.http_client - INFO - 获取股票数据: 601427, 日期: 20250630
2025-07-01 10:16:35 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:35 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601427'}
2025-07-01 10:16:35 - core.http_client - DEBUG - 股票 601427 数据获取成功
2025-07-01 10:16:35 - core.http_client - INFO - 获取股票数据: 000985, 日期: 20250630
2025-07-01 10:16:37 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:37 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000985'}
2025-07-01 10:16:37 - core.http_client - DEBUG - 股票 000985 数据获取成功
2025-07-01 10:16:37 - core.http_client - INFO - 获取股票数据: 300452, 日期: 20250630
2025-07-01 10:16:38 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:38 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300452'}
2025-07-01 10:16:38 - core.http_client - DEBUG - 股票 300452 数据获取成功
2025-07-01 10:16:38 - core.http_client - INFO - 获取股票数据: 300410, 日期: 20250630
2025-07-01 10:16:39 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:39 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300410'}
2025-07-01 10:16:39 - core.http_client - DEBUG - 股票 300410 数据获取成功
2025-07-01 10:16:39 - core.http_client - INFO - 获取股票数据: 600944, 日期: 20250630
2025-07-01 10:16:40 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:40 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600944'}
2025-07-01 10:16:40 - core.http_client - DEBUG - 股票 600944 数据获取成功
2025-07-01 10:16:40 - core.http_client - INFO - 获取股票数据: 602431, 日期: 20250630
2025-07-01 10:16:41 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:41 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '602431'}
2025-07-01 10:16:41 - core.http_client - DEBUG - 股票 602431 数据获取成功
2025-07-01 10:16:41 - core.http_client - INFO - 获取股票数据: 602316, 日期: 20250630
2025-07-01 10:16:43 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:43 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '602316'}
2025-07-01 10:16:43 - core.http_client - DEBUG - 股票 602316 数据获取成功
2025-07-01 10:16:43 - core.http_client - INFO - 获取股票数据: 300916, 日期: 20250630
2025-07-01 10:16:44 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:44 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300916'}
2025-07-01 10:16:44 - core.http_client - DEBUG - 股票 300916 数据获取成功
2025-07-01 10:16:44 - core.http_client - INFO - 获取股票数据: 688441, 日期: 20250630
2025-07-01 10:16:45 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:45 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688441'}
2025-07-01 10:16:45 - core.http_client - DEBUG - 股票 688441 数据获取成功
2025-07-01 10:16:45 - core.http_client - INFO - 获取股票数据: 430083, 日期: 20250630
2025-07-01 10:16:46 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:46 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '430083'}
2025-07-01 10:16:46 - core.http_client - DEBUG - 股票 430083 数据获取成功
2025-07-01 10:16:46 - core.http_client - INFO - 获取股票数据: 301359, 日期: 20250630
2025-07-01 10:16:47 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:47 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301359'}
2025-07-01 10:16:47 - core.http_client - DEBUG - 股票 301359 数据获取成功
2025-07-01 10:16:47 - core.http_client - INFO - 获取股票数据: 688653, 日期: 20250630
2025-07-01 10:16:49 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:49 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688653'}
2025-07-01 10:16:49 - core.http_client - DEBUG - 股票 688653 数据获取成功
2025-07-01 10:16:50 - core.http_client - INFO - 获取股票数据: 002732, 日期: 20250630
2025-07-01 10:16:51 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:51 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002732'}
2025-07-01 10:16:51 - core.http_client - DEBUG - 股票 002732 数据获取成功
2025-07-01 10:16:51 - core.http_client - INFO - 获取股票数据: 688813, 日期: 20250630
2025-07-01 10:16:52 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:52 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688813'}
2025-07-01 10:16:52 - core.http_client - DEBUG - 股票 688813 数据获取成功
2025-07-01 10:16:52 - core.http_client - INFO - 获取股票数据: 600122, 日期: 20250630
2025-07-01 10:16:53 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:53 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600122'}
2025-07-01 10:16:53 - core.http_client - DEBUG - 股票 600122 数据获取成功
2025-07-01 10:16:53 - core.http_client - INFO - 获取股票数据: 300476, 日期: 20250630
2025-07-01 10:16:54 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:54 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300476'}
2025-07-01 10:16:54 - core.http_client - DEBUG - 股票 300476 数据获取成功
2025-07-01 10:16:54 - core.http_client - INFO - 获取股票数据: 601892, 日期: 20250630
2025-07-01 10:16:55 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:55 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601892'}
2025-07-01 10:16:55 - core.http_client - DEBUG - 股票 601892 数据获取成功
2025-07-01 10:16:56 - core.http_client - INFO - 获取股票数据: 601061, 日期: 20250630
2025-07-01 10:16:57 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:57 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601061'}
2025-07-01 10:16:57 - core.http_client - DEBUG - 股票 601061 数据获取成功
2025-07-01 10:16:57 - core.http_client - INFO - 获取股票数据: 602906, 日期: 20250630
2025-07-01 10:16:58 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:58 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '602906'}
2025-07-01 10:16:58 - core.http_client - DEBUG - 股票 602906 数据获取成功
2025-07-01 10:16:58 - core.http_client - INFO - 获取股票数据: 002302, 日期: 20250630
2025-07-01 10:16:59 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:16:59 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002302'}
2025-07-01 10:16:59 - core.http_client - DEBUG - 股票 002302 数据获取成功
2025-07-01 10:16:59 - core.http_client - INFO - 获取股票数据: 000328, 日期: 20250630
2025-07-01 10:17:00 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:00 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000328'}
2025-07-01 10:17:00 - core.http_client - DEBUG - 股票 000328 数据获取成功
2025-07-01 10:17:00 - core.http_client - INFO - 获取股票数据: 300239, 日期: 20250630
2025-07-01 10:17:01 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:01 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300239'}
2025-07-01 10:17:01 - core.http_client - DEBUG - 股票 300239 数据获取成功
2025-07-01 10:17:01 - core.http_client - INFO - 获取股票数据: 600156, 日期: 20250630
2025-07-01 10:17:02 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:02 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600156'}
2025-07-01 10:17:03 - core.http_client - DEBUG - 股票 600156 数据获取成功
2025-07-01 10:17:03 - core.http_client - INFO - 获取股票数据: 601931, 日期: 20250630
2025-07-01 10:17:04 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:04 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601931'}
2025-07-01 10:17:04 - core.http_client - DEBUG - 股票 601931 数据获取成功
2025-07-01 10:17:04 - core.http_client - INFO - 获取股票数据: 602876, 日期: 20250630
2025-07-01 10:17:05 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:05 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '602876'}
2025-07-01 10:17:05 - core.http_client - DEBUG - 股票 602876 数据获取成功
2025-07-01 10:17:05 - core.http_client - INFO - 获取股票数据: 602113, 日期: 20250630
2025-07-01 10:17:06 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:06 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '602113'}
2025-07-01 10:17:06 - core.http_client - DEBUG - 股票 602113 数据获取成功
2025-07-01 10:17:06 - core.http_client - INFO - 获取股票数据: 300690, 日期: 20250630
2025-07-01 10:17:07 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:07 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300690'}
2025-07-01 10:17:07 - core.http_client - DEBUG - 股票 300690 数据获取成功
2025-07-01 10:17:07 - core.http_client - INFO - 获取股票数据: 301721, 日期: 20250630
2025-07-01 10:17:08 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:08 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301721'}
2025-07-01 10:17:08 - core.http_client - DEBUG - 股票 301721 数据获取成功
2025-07-01 10:17:08 - core.http_client - INFO - 获取股票数据: 301447, 日期: 20250630
2025-07-01 10:17:10 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:10 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301447'}
2025-07-01 10:17:10 - core.http_client - DEBUG - 股票 301447 数据获取成功
2025-07-01 10:17:10 - core.http_client - INFO - 获取股票数据: 601723, 日期: 20250630
2025-07-01 10:17:11 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:11 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601723'}
2025-07-01 10:17:11 - core.http_client - DEBUG - 股票 601723 数据获取成功
2025-07-01 10:17:11 - core.http_client - INFO - 获取股票数据: 300120, 日期: 20250630
2025-07-01 10:17:12 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:12 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300120'}
2025-07-01 10:17:12 - core.http_client - DEBUG - 股票 300120 数据获取成功
2025-07-01 10:17:12 - core.http_client - INFO - 获取股票数据: 301687, 日期: 20250630
2025-07-01 10:17:13 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:13 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301687'}
2025-07-01 10:17:13 - core.http_client - DEBUG - 股票 301687 数据获取成功
2025-07-01 10:17:14 - core.http_client - INFO - 获取股票数据: 602744, 日期: 20250630
2025-07-01 10:17:15 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:15 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '602744'}
2025-07-01 10:17:15 - core.http_client - DEBUG - 股票 602744 数据获取成功
2025-07-01 10:17:15 - core.http_client - INFO - 获取股票数据: 601616, 日期: 20250630
2025-07-01 10:17:16 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:16 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601616'}
2025-07-01 10:17:16 - core.http_client - DEBUG - 股票 601616 数据获取成功
2025-07-01 10:17:17 - core.http_client - INFO - 获取股票数据: 688740, 日期: 20250630
2025-07-01 10:17:18 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:18 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688740'}
2025-07-01 10:17:18 - core.http_client - DEBUG - 股票 688740 数据获取成功
2025-07-01 10:17:18 - core.http_client - INFO - 获取股票数据: 688656, 日期: 20250630
2025-07-01 10:17:19 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:19 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688656'}
2025-07-01 10:17:19 - core.http_client - DEBUG - 股票 688656 数据获取成功
2025-07-01 10:17:19 - core.http_client - INFO - 获取股票数据: 301960, 日期: 20250630
2025-07-01 10:17:20 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:20 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301960'}
2025-07-01 10:17:20 - core.http_client - DEBUG - 股票 301960 数据获取成功
2025-07-01 10:17:20 - core.http_client - INFO - 获取股票数据: 000637, 日期: 20250630
2025-07-01 10:17:21 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:21 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000637'}
2025-07-01 10:17:21 - core.http_client - DEBUG - 股票 000637 数据获取成功
2025-07-01 10:17:21 - core.http_client - INFO - 获取股票数据: 601313, 日期: 20250630
2025-07-01 10:17:22 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:22 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601313'}
2025-07-01 10:17:22 - core.http_client - DEBUG - 股票 601313 数据获取成功
2025-07-01 10:17:22 - core.http_client - INFO - 获取股票数据: 301450, 日期: 20250630
2025-07-01 10:17:23 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:23 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301450'}
2025-07-01 10:17:23 - core.http_client - DEBUG - 股票 301450 数据获取成功
2025-07-01 10:17:24 - core.http_client - INFO - 获取股票数据: 688832, 日期: 20250630
2025-07-01 10:17:25 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:25 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688832'}
2025-07-01 10:17:25 - core.http_client - DEBUG - 股票 688832 数据获取成功
2025-07-01 10:17:25 - core.http_client - INFO - 获取股票数据: 688484, 日期: 20250630
2025-07-01 10:17:26 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:26 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688484'}
2025-07-01 10:17:26 - core.http_client - DEBUG - 股票 688484 数据获取成功
2025-07-01 10:17:26 - core.http_client - INFO - 获取股票数据: 000895, 日期: 20250630
2025-07-01 10:17:27 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:27 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000895'}
2025-07-01 10:17:27 - core.http_client - DEBUG - 股票 000895 数据获取成功
2025-07-01 10:17:27 - core.http_client - INFO - 获取股票数据: 600988, 日期: 20250630
2025-07-01 10:17:28 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:28 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600988'}
2025-07-01 10:17:28 - core.http_client - DEBUG - 股票 600988 数据获取成功
2025-07-01 10:17:28 - core.http_client - INFO - 获取股票数据: 602961, 日期: 20250630
2025-07-01 10:17:29 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:29 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '602961'}
2025-07-01 10:17:29 - core.http_client - DEBUG - 股票 602961 数据获取成功
2025-07-01 10:17:29 - core.http_client - INFO - 获取股票数据: 300165, 日期: 20250630
2025-07-01 10:17:31 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:31 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300165'}
2025-07-01 10:17:31 - core.http_client - DEBUG - 股票 300165 数据获取成功
2025-07-01 10:17:31 - core.http_client - INFO - 获取股票数据: 688255, 日期: 20250630
2025-07-01 10:17:32 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:32 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688255'}
2025-07-01 10:17:32 - core.http_client - DEBUG - 股票 688255 数据获取成功
2025-07-01 10:17:32 - core.http_client - INFO - 获取股票数据: 603918, 日期: 20250630
2025-07-01 10:17:33 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:33 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '603918'}
2025-07-01 10:17:33 - core.http_client - DEBUG - 股票 603918 数据获取成功
2025-07-01 10:17:33 - core.http_client - INFO - 获取股票数据: 002609, 日期: 20250630
2025-07-01 10:17:34 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:34 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002609'}
2025-07-01 10:17:34 - core.http_client - DEBUG - 股票 002609 数据获取成功
2025-07-01 10:17:34 - core.http_client - INFO - 获取股票数据: 002776, 日期: 20250630
2025-07-01 10:17:35 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:35 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002776'}
2025-07-01 10:17:35 - core.http_client - DEBUG - 股票 002776 数据获取成功
2025-07-01 10:17:35 - core.http_client - INFO - 获取股票数据: 603194, 日期: 20250630
2025-07-01 10:17:36 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:36 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '603194'}
2025-07-01 10:17:36 - core.http_client - DEBUG - 股票 603194 数据获取成功
2025-07-01 10:17:37 - core.http_client - INFO - 获取股票数据: 602015, 日期: 20250630
2025-07-01 10:17:38 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:38 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '602015'}
2025-07-01 10:17:38 - core.http_client - DEBUG - 股票 602015 数据获取成功
2025-07-01 10:17:39 - core.http_client - INFO - 获取股票数据: 301953, 日期: 20250630
2025-07-01 10:17:40 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:40 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301953'}
2025-07-01 10:17:40 - core.http_client - DEBUG - 股票 301953 数据获取成功
2025-07-01 10:17:40 - core.http_client - INFO - 获取股票数据: 600745, 日期: 20250630
2025-07-01 10:17:41 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:41 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600745'}
2025-07-01 10:17:41 - core.http_client - DEBUG - 股票 600745 数据获取成功
2025-07-01 10:17:41 - core.http_client - INFO - 获取股票数据: 601878, 日期: 20250630
2025-07-01 10:17:42 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:42 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601878'}
2025-07-01 10:17:42 - core.http_client - DEBUG - 股票 601878 数据获取成功
2025-07-01 10:17:42 - core.http_client - INFO - 获取股票数据: 603145, 日期: 20250630
2025-07-01 10:17:43 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:43 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '603145'}
2025-07-01 10:17:43 - core.http_client - DEBUG - 股票 603145 数据获取成功
2025-07-01 10:17:43 - core.http_client - INFO - 获取股票数据: 301411, 日期: 20250630
2025-07-01 10:17:45 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:45 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301411'}
2025-07-01 10:17:45 - core.http_client - DEBUG - 股票 301411 数据获取成功
2025-07-01 10:17:45 - core.http_client - INFO - 获取股票数据: 603703, 日期: 20250630
2025-07-01 10:17:46 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:46 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '603703'}
2025-07-01 10:17:46 - core.http_client - DEBUG - 股票 603703 数据获取成功
2025-07-01 10:17:46 - core.http_client - INFO - 获取股票数据: 000314, 日期: 20250630
2025-07-01 10:17:47 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:47 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000314'}
2025-07-01 10:17:47 - core.http_client - DEBUG - 股票 000314 数据获取成功
2025-07-01 10:17:47 - core.http_client - INFO - 获取股票数据: 603103, 日期: 20250630
2025-07-01 10:17:48 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:48 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '603103'}
2025-07-01 10:17:48 - core.http_client - DEBUG - 股票 603103 数据获取成功
2025-07-01 10:17:48 - core.http_client - INFO - 获取股票数据: 002758, 日期: 20250630
2025-07-01 10:17:49 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:49 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002758'}
2025-07-01 10:17:49 - core.http_client - DEBUG - 股票 002758 数据获取成功
2025-07-01 10:17:49 - core.http_client - INFO - 获取股票数据: 603573, 日期: 20250630
2025-07-01 10:17:50 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:50 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '603573'}
2025-07-01 10:17:50 - core.http_client - DEBUG - 股票 603573 数据获取成功
2025-07-01 10:17:51 - core.http_client - INFO - 获取股票数据: 688668, 日期: 20250630
2025-07-01 10:17:52 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:52 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688668'}
2025-07-01 10:17:52 - core.http_client - DEBUG - 股票 688668 数据获取成功
2025-07-01 10:17:52 - core.http_client - INFO - 获取股票数据: 301307, 日期: 20250630
2025-07-01 10:17:53 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:53 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301307'}
2025-07-01 10:17:53 - core.http_client - DEBUG - 股票 301307 数据获取成功
2025-07-01 10:17:53 - core.http_client - INFO - 获取股票数据: 000825, 日期: 20250630
2025-07-01 10:17:54 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:54 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000825'}
2025-07-01 10:17:54 - core.http_client - DEBUG - 股票 000825 数据获取成功
2025-07-01 10:17:54 - core.http_client - INFO - 获取股票数据: 000040, 日期: 20250630
2025-07-01 10:17:55 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:55 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000040'}
2025-07-01 10:17:55 - core.http_client - DEBUG - 股票 000040 数据获取成功
2025-07-01 10:17:55 - core.http_client - INFO - 获取股票数据: 301637, 日期: 20250630
2025-07-01 10:17:56 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:56 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301637'}
2025-07-01 10:17:56 - core.http_client - DEBUG - 股票 301637 数据获取成功
2025-07-01 10:17:56 - core.http_client - INFO - 获取股票数据: 002971, 日期: 20250630
2025-07-01 10:17:57 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:57 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002971'}
2025-07-01 10:17:57 - core.http_client - DEBUG - 股票 002971 数据获取成功
2025-07-01 10:17:58 - core.http_client - INFO - 获取股票数据: 602722, 日期: 20250630
2025-07-01 10:17:59 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:17:59 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '602722'}
2025-07-01 10:17:59 - core.http_client - DEBUG - 股票 602722 数据获取成功
2025-07-01 10:17:59 - core.http_client - INFO - 获取股票数据: 688547, 日期: 20250630
2025-07-01 10:18:00 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:00 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688547'}
2025-07-01 10:18:00 - core.http_client - DEBUG - 股票 688547 数据获取成功
2025-07-01 10:18:00 - core.http_client - INFO - 获取股票数据: 602481, 日期: 20250630
2025-07-01 10:18:01 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:01 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '602481'}
2025-07-01 10:18:01 - core.http_client - DEBUG - 股票 602481 数据获取成功
2025-07-01 10:18:01 - core.http_client - INFO - 获取股票数据: 000836, 日期: 20250630
2025-07-01 10:18:02 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:02 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000836'}
2025-07-01 10:18:02 - core.http_client - DEBUG - 股票 000836 数据获取成功
2025-07-01 10:18:03 - core.http_client - INFO - 获取股票数据: 300520, 日期: 20250630
2025-07-01 10:18:04 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:04 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300520'}
2025-07-01 10:18:04 - core.http_client - DEBUG - 股票 300520 数据获取成功
2025-07-01 10:18:04 - core.http_client - INFO - 获取股票数据: 600881, 日期: 20250630
2025-07-01 10:18:06 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:06 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600881'}
2025-07-01 10:18:06 - core.http_client - DEBUG - 股票 600881 数据获取成功
2025-07-01 10:18:06 - core.http_client - INFO - 获取股票数据: 603391, 日期: 20250630
2025-07-01 10:18:07 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:07 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '603391'}
2025-07-01 10:18:07 - core.http_client - DEBUG - 股票 603391 数据获取成功
2025-07-01 10:18:07 - core.http_client - INFO - 获取股票数据: 601982, 日期: 20250630
2025-07-01 10:18:08 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:08 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601982'}
2025-07-01 10:18:08 - core.http_client - DEBUG - 股票 601982 数据获取成功
2025-07-01 10:18:08 - core.http_client - INFO - 获取股票数据: 688390, 日期: 20250630
2025-07-01 10:18:09 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:09 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688390'}
2025-07-01 10:18:09 - core.http_client - DEBUG - 股票 688390 数据获取成功
2025-07-01 10:18:09 - core.http_client - INFO - 获取股票数据: 301796, 日期: 20250630
2025-07-01 10:18:10 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:10 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301796'}
2025-07-01 10:18:10 - core.http_client - DEBUG - 股票 301796 数据获取成功
2025-07-01 10:18:10 - core.http_client - INFO - 获取股票数据: 688069, 日期: 20250630
2025-07-01 10:18:11 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:11 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688069'}
2025-07-01 10:18:11 - core.http_client - DEBUG - 股票 688069 数据获取成功
2025-07-01 10:18:12 - core.http_client - INFO - 获取股票数据: 000477, 日期: 20250630
2025-07-01 10:18:13 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:13 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000477'}
2025-07-01 10:18:13 - core.http_client - DEBUG - 股票 000477 数据获取成功
2025-07-01 10:18:13 - core.http_client - INFO - 获取股票数据: 601442, 日期: 20250630
2025-07-01 10:18:14 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:14 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601442'}
2025-07-01 10:18:14 - core.http_client - DEBUG - 股票 601442 数据获取成功
2025-07-01 10:18:14 - core.http_client - INFO - 获取股票数据: 600615, 日期: 20250630
2025-07-01 10:18:15 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:15 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600615'}
2025-07-01 10:18:15 - core.http_client - DEBUG - 股票 600615 数据获取成功
2025-07-01 10:18:15 - core.http_client - INFO - 获取股票数据: 602449, 日期: 20250630
2025-07-01 10:18:16 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:16 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '602449'}
2025-07-01 10:18:16 - core.http_client - DEBUG - 股票 602449 数据获取成功
2025-07-01 10:18:16 - core.http_client - INFO - 获取股票数据: 600873, 日期: 20250630
2025-07-01 10:18:17 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:17 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600873'}
2025-07-01 10:18:17 - core.http_client - DEBUG - 股票 600873 数据获取成功
2025-07-01 10:18:17 - core.http_client - INFO - 获取股票数据: 688746, 日期: 20250630
2025-07-01 10:18:19 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:19 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688746'}
2025-07-01 10:18:19 - core.http_client - DEBUG - 股票 688746 数据获取成功
2025-07-01 10:18:19 - core.http_client - INFO - 获取股票数据: 301544, 日期: 20250630
2025-07-01 10:18:20 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:20 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301544'}
2025-07-01 10:18:20 - core.http_client - DEBUG - 股票 301544 数据获取成功
2025-07-01 10:18:20 - core.http_client - INFO - 获取股票数据: 002513, 日期: 20250630
2025-07-01 10:18:21 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:21 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002513'}
2025-07-01 10:18:21 - core.http_client - DEBUG - 股票 002513 数据获取成功
2025-07-01 10:18:21 - core.http_client - INFO - 获取股票数据: 301234, 日期: 20250630
2025-07-01 10:18:22 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:22 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301234'}
2025-07-01 10:18:22 - core.http_client - DEBUG - 股票 301234 数据获取成功
2025-07-01 10:18:22 - core.http_client - INFO - 获取股票数据: 600094, 日期: 20250630
2025-07-01 10:18:23 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:23 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600094'}
2025-07-01 10:18:23 - core.http_client - DEBUG - 股票 600094 数据获取成功
2025-07-01 10:18:23 - core.http_client - INFO - 获取股票数据: 688045, 日期: 20250630
2025-07-01 10:18:24 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:24 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688045'}
2025-07-01 10:18:24 - core.http_client - DEBUG - 股票 688045 数据获取成功
2025-07-01 10:18:25 - core.http_client - INFO - 获取股票数据: 000349, 日期: 20250630
2025-07-01 10:18:26 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:26 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000349'}
2025-07-01 10:18:26 - core.http_client - DEBUG - 股票 000349 数据获取成功
2025-07-01 10:18:26 - core.http_client - INFO - 获取股票数据: 601363, 日期: 20250630
2025-07-01 10:18:27 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:27 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601363'}
2025-07-01 10:18:27 - core.http_client - DEBUG - 股票 601363 数据获取成功
2025-07-01 10:18:28 - core.http_client - INFO - 获取股票数据: 300613, 日期: 20250630
2025-07-01 10:18:29 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:29 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300613'}
2025-07-01 10:18:29 - core.http_client - DEBUG - 股票 300613 数据获取成功
2025-07-01 10:18:29 - core.http_client - INFO - 获取股票数据: 000318, 日期: 20250630
2025-07-01 10:18:30 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:30 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000318'}
2025-07-01 10:18:30 - core.http_client - DEBUG - 股票 000318 数据获取成功
2025-07-01 10:18:30 - core.http_client - INFO - 获取股票数据: 301941, 日期: 20250630
2025-07-01 10:18:31 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:31 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301941'}
2025-07-01 10:18:31 - core.http_client - DEBUG - 股票 301941 数据获取成功
2025-07-01 10:18:31 - core.http_client - INFO - 获取股票数据: 603191, 日期: 20250630
2025-07-01 10:18:33 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:33 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '603191'}
2025-07-01 10:18:33 - core.http_client - DEBUG - 股票 603191 数据获取成功
2025-07-01 10:18:33 - core.http_client - INFO - 获取股票数据: 301169, 日期: 20250630
2025-07-01 10:18:34 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:34 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301169'}
2025-07-01 10:18:34 - core.http_client - DEBUG - 股票 301169 数据获取成功
2025-07-01 10:18:34 - core.http_client - INFO - 获取股票数据: 602572, 日期: 20250630
2025-07-01 10:18:35 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:35 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '602572'}
2025-07-01 10:18:35 - core.http_client - DEBUG - 股票 602572 数据获取成功
2025-07-01 10:18:35 - core.http_client - INFO - 获取股票数据: 300019, 日期: 20250630
2025-07-01 10:18:36 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:36 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300019'}
2025-07-01 10:18:36 - core.http_client - DEBUG - 股票 300019 数据获取成功
2025-07-01 10:18:36 - core.http_client - INFO - 获取股票数据: 602333, 日期: 20250630
2025-07-01 10:18:37 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:37 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '602333'}
2025-07-01 10:18:37 - core.http_client - DEBUG - 股票 602333 数据获取成功
2025-07-01 10:18:37 - core.http_client - INFO - 获取股票数据: 603230, 日期: 20250630
2025-07-01 10:18:39 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:39 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '603230'}
2025-07-01 10:18:39 - core.http_client - DEBUG - 股票 603230 数据获取成功
2025-07-01 10:18:39 - core.http_client - INFO - 获取股票数据: 688272, 日期: 20250630
2025-07-01 10:18:40 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:40 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688272'}
2025-07-01 10:18:40 - core.http_client - DEBUG - 股票 688272 数据获取成功
2025-07-01 10:18:40 - core.http_client - INFO - 获取股票数据: 301613, 日期: 20250630
2025-07-01 10:18:41 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:41 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301613'}
2025-07-01 10:18:41 - core.http_client - DEBUG - 股票 301613 数据获取成功
2025-07-01 10:18:41 - core.http_client - INFO - 获取股票数据: 301343, 日期: 20250630
2025-07-01 10:18:42 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:42 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301343'}
2025-07-01 10:18:42 - core.http_client - DEBUG - 股票 301343 数据获取成功
2025-07-01 10:18:42 - core.http_client - INFO - 获取股票数据: 000266, 日期: 20250630
2025-07-01 10:18:43 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:43 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000266'}
2025-07-01 10:18:43 - core.http_client - DEBUG - 股票 000266 数据获取成功
2025-07-01 10:18:43 - core.http_client - INFO - 获取股票数据: 601448, 日期: 20250630
2025-07-01 10:18:44 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:44 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601448'}
2025-07-01 10:18:44 - core.http_client - DEBUG - 股票 601448 数据获取成功
2025-07-01 10:18:45 - core.http_client - INFO - 获取股票数据: 000450, 日期: 20250630
2025-07-01 10:18:46 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:46 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000450'}
2025-07-01 10:18:46 - core.http_client - DEBUG - 股票 000450 数据获取成功
2025-07-01 10:18:46 - core.http_client - INFO - 获取股票数据: 000214, 日期: 20250630
2025-07-01 10:18:47 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:47 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000214'}
2025-07-01 10:18:47 - core.http_client - DEBUG - 股票 000214 数据获取成功
2025-07-01 10:18:47 - core.http_client - INFO - 获取股票数据: 300960, 日期: 20250630
2025-07-01 10:18:48 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:48 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300960'}
2025-07-01 10:18:48 - core.http_client - DEBUG - 股票 300960 数据获取成功
2025-07-01 10:18:48 - core.http_client - INFO - 获取股票数据: 002332, 日期: 20250630
2025-07-01 10:18:49 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:49 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002332'}
2025-07-01 10:18:49 - core.http_client - DEBUG - 股票 002332 数据获取成功
2025-07-01 10:18:49 - core.http_client - INFO - 获取股票数据: 300648, 日期: 20250630
2025-07-01 10:18:50 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:50 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300648'}
2025-07-01 10:18:50 - core.http_client - DEBUG - 股票 300648 数据获取成功
2025-07-01 10:18:50 - core.http_client - INFO - 获取股票数据: 301575, 日期: 20250630
2025-07-01 10:18:51 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:51 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301575'}
2025-07-01 10:18:51 - core.http_client - DEBUG - 股票 301575 数据获取成功
2025-07-01 10:18:53 - core.http_client - INFO - 获取股票数据: 688810, 日期: 20250630
2025-07-01 10:18:54 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:54 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688810'}
2025-07-01 10:18:54 - core.http_client - DEBUG - 股票 688810 数据获取成功
2025-07-01 10:18:54 - core.http_client - INFO - 获取股票数据: 300497, 日期: 20250630
2025-07-01 10:18:55 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:55 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300497'}
2025-07-01 10:18:55 - core.http_client - DEBUG - 股票 300497 数据获取成功
2025-07-01 10:18:55 - core.http_client - INFO - 获取股票数据: 000859, 日期: 20250630
2025-07-01 10:18:56 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:56 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000859'}
2025-07-01 10:18:56 - core.http_client - DEBUG - 股票 000859 数据获取成功
2025-07-01 10:18:56 - core.http_client - INFO - 获取股票数据: 688207, 日期: 20250630
2025-07-01 10:18:57 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:57 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688207'}
2025-07-01 10:18:57 - core.http_client - DEBUG - 股票 688207 数据获取成功
2025-07-01 10:18:57 - core.http_client - INFO - 获取股票数据: 601455, 日期: 20250630
2025-07-01 10:18:58 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:58 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601455'}
2025-07-01 10:18:58 - core.http_client - DEBUG - 股票 601455 数据获取成功
2025-07-01 10:18:58 - core.http_client - INFO - 获取股票数据: 600734, 日期: 20250630
2025-07-01 10:18:59 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:18:59 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600734'}
2025-07-01 10:18:59 - core.http_client - DEBUG - 股票 600734 数据获取成功
2025-07-01 10:19:00 - core.http_client - INFO - 获取股票数据: 300973, 日期: 20250630
2025-07-01 10:19:01 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:01 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300973'}
2025-07-01 10:19:01 - core.http_client - DEBUG - 股票 300973 数据获取成功
2025-07-01 10:19:01 - core.http_client - INFO - 获取股票数据: 301513, 日期: 20250630
2025-07-01 10:19:02 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:02 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301513'}
2025-07-01 10:19:02 - core.http_client - DEBUG - 股票 301513 数据获取成功
2025-07-01 10:19:02 - core.http_client - INFO - 获取股票数据: 600299, 日期: 20250630
2025-07-01 10:19:03 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:03 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600299'}
2025-07-01 10:19:03 - core.http_client - DEBUG - 股票 600299 数据获取成功
2025-07-01 10:19:03 - core.http_client - INFO - 获取股票数据: 603544, 日期: 20250630
2025-07-01 10:19:04 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:04 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '603544'}
2025-07-01 10:19:04 - core.http_client - DEBUG - 股票 603544 数据获取成功
2025-07-01 10:19:04 - core.http_client - INFO - 获取股票数据: 301364, 日期: 20250630
2025-07-01 10:19:05 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:05 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301364'}
2025-07-01 10:19:05 - core.http_client - DEBUG - 股票 301364 数据获取成功
2025-07-01 10:19:05 - core.http_client - INFO - 获取股票数据: 600997, 日期: 20250630
2025-07-01 10:19:07 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:07 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600997'}
2025-07-01 10:19:07 - core.http_client - DEBUG - 股票 600997 数据获取成功
2025-07-01 10:19:07 - core.http_client - INFO - 获取股票数据: 688824, 日期: 20250630
2025-07-01 10:19:08 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:08 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688824'}
2025-07-01 10:19:08 - core.http_client - DEBUG - 股票 688824 数据获取成功
2025-07-01 10:19:08 - core.http_client - INFO - 获取股票数据: 301743, 日期: 20250630
2025-07-01 10:19:09 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:09 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301743'}
2025-07-01 10:19:09 - core.http_client - DEBUG - 股票 301743 数据获取成功
2025-07-01 10:19:09 - core.http_client - INFO - 获取股票数据: 601138, 日期: 20250630
2025-07-01 10:19:10 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:10 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601138'}
2025-07-01 10:19:10 - core.http_client - DEBUG - 股票 601138 数据获取成功
2025-07-01 10:19:10 - core.http_client - INFO - 获取股票数据: 000655, 日期: 20250630
2025-07-01 10:19:11 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:11 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000655'}
2025-07-01 10:19:11 - core.http_client - DEBUG - 股票 000655 数据获取成功
2025-07-01 10:19:11 - core.http_client - INFO - 获取股票数据: 603619, 日期: 20250630
2025-07-01 10:19:12 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:12 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '603619'}
2025-07-01 10:19:13 - core.http_client - DEBUG - 股票 603619 数据获取成功
2025-07-01 10:19:13 - core.http_client - INFO - 获取股票数据: 002387, 日期: 20250630
2025-07-01 10:19:14 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:14 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002387'}
2025-07-01 10:19:14 - core.http_client - DEBUG - 股票 002387 数据获取成功
2025-07-01 10:19:14 - core.http_client - INFO - 获取股票数据: 000642, 日期: 20250630
2025-07-01 10:19:15 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:15 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000642'}
2025-07-01 10:19:15 - core.http_client - DEBUG - 股票 000642 数据获取成功
2025-07-01 10:19:15 - core.http_client - INFO - 获取股票数据: 600722, 日期: 20250630
2025-07-01 10:19:16 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:16 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600722'}
2025-07-01 10:19:16 - core.http_client - DEBUG - 股票 600722 数据获取成功
2025-07-01 10:19:17 - core.http_client - INFO - 获取股票数据: 602488, 日期: 20250630
2025-07-01 10:19:18 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:18 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '602488'}
2025-07-01 10:19:18 - core.http_client - DEBUG - 股票 602488 数据获取成功
2025-07-01 10:19:18 - core.http_client - INFO - 获取股票数据: 000849, 日期: 20250630
2025-07-01 10:19:19 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:19 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000849'}
2025-07-01 10:19:19 - core.http_client - DEBUG - 股票 000849 数据获取成功
2025-07-01 10:19:20 - core.http_client - INFO - 获取股票数据: 601956, 日期: 20250630
2025-07-01 10:19:21 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:21 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601956'}
2025-07-01 10:19:21 - core.http_client - DEBUG - 股票 601956 数据获取成功
2025-07-01 10:19:21 - core.http_client - INFO - 获取股票数据: 688958, 日期: 20250630
2025-07-01 10:19:22 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:22 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688958'}
2025-07-01 10:19:22 - core.http_client - DEBUG - 股票 688958 数据获取成功
2025-07-01 10:19:22 - core.http_client - INFO - 获取股票数据: 688176, 日期: 20250630
2025-07-01 10:19:23 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:23 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688176'}
2025-07-01 10:19:23 - core.http_client - DEBUG - 股票 688176 数据获取成功
2025-07-01 10:19:23 - core.http_client - INFO - 获取股票数据: 688134, 日期: 20250630
2025-07-01 10:19:24 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:24 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688134'}
2025-07-01 10:19:24 - core.http_client - DEBUG - 股票 688134 数据获取成功
2025-07-01 10:19:24 - core.http_client - INFO - 获取股票数据: 601622, 日期: 20250630
2025-07-01 10:19:25 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:25 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601622'}
2025-07-01 10:19:25 - core.http_client - DEBUG - 股票 601622 数据获取成功
2025-07-01 10:19:25 - core.http_client - INFO - 获取股票数据: 688812, 日期: 20250630
2025-07-01 10:19:26 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:26 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688812'}
2025-07-01 10:19:26 - core.http_client - DEBUG - 股票 688812 数据获取成功
2025-07-01 10:19:27 - core.http_client - INFO - 获取股票数据: 300434, 日期: 20250630
2025-07-01 10:19:28 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:28 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300434'}
2025-07-01 10:19:28 - core.http_client - DEBUG - 股票 300434 数据获取成功
2025-07-01 10:19:28 - core.http_client - INFO - 获取股票数据: 602881, 日期: 20250630
2025-07-01 10:19:29 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:29 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '602881'}
2025-07-01 10:19:29 - core.http_client - DEBUG - 股票 602881 数据获取成功
2025-07-01 10:19:29 - core.http_client - INFO - 获取股票数据: 002046, 日期: 20250630
2025-07-01 10:19:30 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:30 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002046'}
2025-07-01 10:19:30 - core.http_client - DEBUG - 股票 002046 数据获取成功
2025-07-01 10:19:30 - core.http_client - INFO - 获取股票数据: 688911, 日期: 20250630
2025-07-01 10:19:31 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:31 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688911'}
2025-07-01 10:19:31 - core.http_client - DEBUG - 股票 688911 数据获取成功
2025-07-01 10:19:31 - core.http_client - INFO - 获取股票数据: 002236, 日期: 20250630
2025-07-01 10:19:32 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:32 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002236'}
2025-07-01 10:19:32 - core.http_client - DEBUG - 股票 002236 数据获取成功
2025-07-01 10:19:32 - core.http_client - INFO - 获取股票数据: 000102, 日期: 20250630
2025-07-01 10:19:33 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:33 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000102'}
2025-07-01 10:19:33 - core.http_client - DEBUG - 股票 000102 数据获取成功
2025-07-01 10:19:34 - core.http_client - INFO - 获取股票数据: 002555, 日期: 20250630
2025-07-01 10:19:35 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:35 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002555'}
2025-07-01 10:19:35 - core.http_client - DEBUG - 股票 002555 数据获取成功
2025-07-01 10:19:35 - core.http_client - INFO - 获取股票数据: 600475, 日期: 20250630
2025-07-01 10:19:36 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:36 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600475'}
2025-07-01 10:19:36 - core.http_client - DEBUG - 股票 600475 数据获取成功
2025-07-01 10:19:36 - core.http_client - INFO - 获取股票数据: 601897, 日期: 20250630
2025-07-01 10:19:37 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:37 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601897'}
2025-07-01 10:19:37 - core.http_client - DEBUG - 股票 601897 数据获取成功
2025-07-01 10:19:37 - core.http_client - INFO - 获取股票数据: 601006, 日期: 20250630
2025-07-01 10:19:38 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:38 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601006'}
2025-07-01 10:19:38 - core.http_client - DEBUG - 股票 601006 数据获取成功
2025-07-01 10:19:38 - core.http_client - INFO - 获取股票数据: 601640, 日期: 20250630
2025-07-01 10:19:39 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:39 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601640'}
2025-07-01 10:19:39 - core.http_client - DEBUG - 股票 601640 数据获取成功
2025-07-01 10:19:39 - core.http_client - INFO - 获取股票数据: 300632, 日期: 20250630
2025-07-01 10:19:41 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:41 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300632'}
2025-07-01 10:19:41 - core.http_client - DEBUG - 股票 300632 数据获取成功
2025-07-01 10:19:42 - core.http_client - INFO - 获取股票数据: 600592, 日期: 20250630
2025-07-01 10:19:43 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:43 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600592'}
2025-07-01 10:19:43 - core.http_client - DEBUG - 股票 600592 数据获取成功
2025-07-01 10:19:43 - core.http_client - INFO - 获取股票数据: 002780, 日期: 20250630
2025-07-01 10:19:44 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:44 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002780'}
2025-07-01 10:19:44 - core.http_client - DEBUG - 股票 002780 数据获取成功
2025-07-01 10:19:44 - core.http_client - INFO - 获取股票数据: 300378, 日期: 20250630
2025-07-01 10:19:45 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:45 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300378'}
2025-07-01 10:19:45 - core.http_client - DEBUG - 股票 300378 数据获取成功
2025-07-01 10:19:45 - core.http_client - INFO - 获取股票数据: 688190, 日期: 20250630
2025-07-01 10:19:46 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:46 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688190'}
2025-07-01 10:19:46 - core.http_client - DEBUG - 股票 688190 数据获取成功
2025-07-01 10:19:46 - core.http_client - INFO - 获取股票数据: 301536, 日期: 20250630
2025-07-01 10:19:48 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:48 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301536'}
2025-07-01 10:19:48 - core.http_client - DEBUG - 股票 301536 数据获取成功
2025-07-01 10:19:48 - core.http_client - INFO - 获取股票数据: 301581, 日期: 20250630
2025-07-01 10:19:49 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:49 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301581'}
2025-07-01 10:19:49 - core.http_client - DEBUG - 股票 301581 数据获取成功
2025-07-01 10:19:49 - core.http_client - INFO - 获取股票数据: 000876, 日期: 20250630
2025-07-01 10:19:50 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:50 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000876'}
2025-07-01 10:19:50 - core.http_client - DEBUG - 股票 000876 数据获取成功
2025-07-01 10:19:50 - core.http_client - INFO - 获取股票数据: 301029, 日期: 20250630
2025-07-01 10:19:51 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:51 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301029'}
2025-07-01 10:19:51 - core.http_client - DEBUG - 股票 301029 数据获取成功
2025-07-01 10:19:51 - core.http_client - INFO - 获取股票数据: 301541, 日期: 20250630
2025-07-01 10:19:52 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:52 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301541'}
2025-07-01 10:19:52 - core.http_client - DEBUG - 股票 301541 数据获取成功
2025-07-01 10:19:52 - core.http_client - INFO - 获取股票数据: 301506, 日期: 20250630
2025-07-01 10:19:53 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:53 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301506'}
2025-07-01 10:19:53 - core.http_client - DEBUG - 股票 301506 数据获取成功
2025-07-01 10:19:54 - core.http_client - INFO - 获取股票数据: 601253, 日期: 20250630
2025-07-01 10:19:55 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:55 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601253'}
2025-07-01 10:19:55 - core.http_client - DEBUG - 股票 601253 数据获取成功
2025-07-01 10:19:55 - core.http_client - INFO - 获取股票数据: 603075, 日期: 20250630
2025-07-01 10:19:56 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:56 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '603075'}
2025-07-01 10:19:56 - core.http_client - DEBUG - 股票 603075 数据获取成功
2025-07-01 10:19:56 - core.http_client - INFO - 获取股票数据: 002783, 日期: 20250630
2025-07-01 10:19:57 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:57 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002783'}
2025-07-01 10:19:57 - core.http_client - DEBUG - 股票 002783 数据获取成功
2025-07-01 10:19:57 - core.http_client - INFO - 获取股票数据: 688842, 日期: 20250630
2025-07-01 10:19:58 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:58 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688842'}
2025-07-01 10:19:58 - core.http_client - DEBUG - 股票 688842 数据获取成功
2025-07-01 10:19:58 - core.http_client - INFO - 获取股票数据: 000438, 日期: 20250630
2025-07-01 10:19:59 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:19:59 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000438'}
2025-07-01 10:19:59 - core.http_client - DEBUG - 股票 000438 数据获取成功
2025-07-01 10:19:59 - core.http_client - INFO - 获取股票数据: 000116, 日期: 20250630
2025-07-01 10:20:00 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:00 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000116'}
2025-07-01 10:20:00 - core.http_client - DEBUG - 股票 000116 数据获取成功
2025-07-01 10:20:00 - core.http_client - INFO - 获取股票数据: 602742, 日期: 20250630
2025-07-01 10:20:02 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:02 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '602742'}
2025-07-01 10:20:02 - core.http_client - DEBUG - 股票 602742 数据获取成功
2025-07-01 10:20:02 - core.http_client - INFO - 获取股票数据: 602771, 日期: 20250630
2025-07-01 10:20:03 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:03 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '602771'}
2025-07-01 10:20:03 - core.http_client - DEBUG - 股票 602771 数据获取成功
2025-07-01 10:20:03 - core.http_client - INFO - 获取股票数据: 600132, 日期: 20250630
2025-07-01 10:20:04 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:04 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600132'}
2025-07-01 10:20:04 - core.http_client - DEBUG - 股票 600132 数据获取成功
2025-07-01 10:20:04 - core.http_client - INFO - 获取股票数据: 600659, 日期: 20250630
2025-07-01 10:20:05 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:05 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600659'}
2025-07-01 10:20:05 - core.http_client - DEBUG - 股票 600659 数据获取成功
2025-07-01 10:20:06 - core.http_client - INFO - 获取股票数据: 300131, 日期: 20250630
2025-07-01 10:20:07 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:07 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300131'}
2025-07-01 10:20:07 - core.http_client - DEBUG - 股票 300131 数据获取成功
2025-07-01 10:20:07 - core.http_client - INFO - 获取股票数据: 602451, 日期: 20250630
2025-07-01 10:20:08 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:08 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '602451'}
2025-07-01 10:20:08 - core.http_client - DEBUG - 股票 602451 数据获取成功
2025-07-01 10:20:09 - core.http_client - INFO - 获取股票数据: 300170, 日期: 20250630
2025-07-01 10:20:10 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:10 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300170'}
2025-07-01 10:20:10 - core.http_client - DEBUG - 股票 300170 数据获取成功
2025-07-01 10:20:10 - core.http_client - INFO - 获取股票数据: 688497, 日期: 20250630
2025-07-01 10:20:11 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:11 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688497'}
2025-07-01 10:20:11 - core.http_client - DEBUG - 股票 688497 数据获取成功
2025-07-01 10:20:11 - core.http_client - INFO - 获取股票数据: 603389, 日期: 20250630
2025-07-01 10:20:12 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:12 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '603389'}
2025-07-01 10:20:12 - core.http_client - DEBUG - 股票 603389 数据获取成功
2025-07-01 10:20:12 - core.http_client - INFO - 获取股票数据: 688008, 日期: 20250630
2025-07-01 10:20:13 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:13 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688008'}
2025-07-01 10:20:13 - core.http_client - DEBUG - 股票 688008 数据获取成功
2025-07-01 10:20:13 - core.http_client - INFO - 获取股票数据: 000234, 日期: 20250630
2025-07-01 10:20:14 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:14 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000234'}
2025-07-01 10:20:14 - core.http_client - DEBUG - 股票 000234 数据获取成功
2025-07-01 10:20:14 - core.http_client - INFO - 获取股票数据: 602575, 日期: 20250630
2025-07-01 10:20:16 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:16 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '602575'}
2025-07-01 10:20:16 - core.http_client - DEBUG - 股票 602575 数据获取成功
2025-07-01 10:20:16 - core.http_client - INFO - 获取股票数据: 000858, 日期: 20250630
2025-07-01 10:20:17 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:17 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000858'}
2025-07-01 10:20:17 - core.http_client - DEBUG - 股票 000858 数据获取成功
2025-07-01 10:20:17 - core.http_client - INFO - 获取股票数据: 603481, 日期: 20250630
2025-07-01 10:20:18 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:18 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '603481'}
2025-07-01 10:20:18 - core.http_client - DEBUG - 股票 603481 数据获取成功
2025-07-01 10:20:18 - core.http_client - INFO - 获取股票数据: 688276, 日期: 20250630
2025-07-01 10:20:19 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:19 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688276'}
2025-07-01 10:20:19 - core.http_client - DEBUG - 股票 688276 数据获取成功
2025-07-01 10:20:19 - core.http_client - INFO - 获取股票数据: 603018, 日期: 20250630
2025-07-01 10:20:20 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:20 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '603018'}
2025-07-01 10:20:20 - core.http_client - DEBUG - 股票 603018 数据获取成功
2025-07-01 10:20:20 - core.http_client - INFO - 获取股票数据: 603515, 日期: 20250630
2025-07-01 10:20:21 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:21 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '603515'}
2025-07-01 10:20:21 - core.http_client - DEBUG - 股票 603515 数据获取成功
2025-07-01 10:20:22 - core.http_client - INFO - 获取股票数据: 000703, 日期: 20250630
2025-07-01 10:20:23 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:23 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000703'}
2025-07-01 10:20:23 - core.http_client - DEBUG - 股票 000703 数据获取成功
2025-07-01 10:20:23 - core.http_client - INFO - 获取股票数据: 000981, 日期: 20250630
2025-07-01 10:20:24 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:24 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000981'}
2025-07-01 10:20:24 - core.http_client - DEBUG - 股票 000981 数据获取成功
2025-07-01 10:20:24 - core.http_client - INFO - 获取股票数据: 301100, 日期: 20250630
2025-07-01 10:20:25 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:25 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301100'}
2025-07-01 10:20:25 - core.http_client - DEBUG - 股票 301100 数据获取成功
2025-07-01 10:20:25 - core.http_client - INFO - 获取股票数据: 601285, 日期: 20250630
2025-07-01 10:20:26 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:26 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601285'}
2025-07-01 10:20:26 - core.http_client - DEBUG - 股票 601285 数据获取成功
2025-07-01 10:20:26 - core.http_client - INFO - 获取股票数据: 002015, 日期: 20250630
2025-07-01 10:20:27 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:27 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002015'}
2025-07-01 10:20:27 - core.http_client - DEBUG - 股票 002015 数据获取成功
2025-07-01 10:20:27 - core.http_client - INFO - 获取股票数据: 000117, 日期: 20250630
2025-07-01 10:20:29 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:29 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000117'}
2025-07-01 10:20:29 - core.http_client - DEBUG - 股票 000117 数据获取成功
2025-07-01 10:20:29 - core.http_client - INFO - 获取股票数据: 601608, 日期: 20250630
2025-07-01 10:20:30 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:30 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601608'}
2025-07-01 10:20:30 - core.http_client - DEBUG - 股票 601608 数据获取成功
2025-07-01 10:20:31 - core.http_client - INFO - 获取股票数据: 000325, 日期: 20250630
2025-07-01 10:20:32 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:32 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000325'}
2025-07-01 10:20:32 - core.http_client - DEBUG - 股票 000325 数据获取成功
2025-07-01 10:20:32 - core.http_client - INFO - 获取股票数据: 600006, 日期: 20250630
2025-07-01 10:20:33 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:33 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600006'}
2025-07-01 10:20:33 - core.http_client - DEBUG - 股票 600006 数据获取成功
2025-07-01 10:20:33 - core.http_client - INFO - 获取股票数据: 601119, 日期: 20250630
2025-07-01 10:20:34 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:34 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601119'}
2025-07-01 10:20:34 - core.http_client - DEBUG - 股票 601119 数据获取成功
2025-07-01 10:20:34 - core.http_client - INFO - 获取股票数据: 603521, 日期: 20250630
2025-07-01 10:20:35 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:35 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '603521'}
2025-07-01 10:20:35 - core.http_client - DEBUG - 股票 603521 数据获取成功
2025-07-01 10:20:36 - core.http_client - INFO - 获取股票数据: 002918, 日期: 20250630
2025-07-01 10:20:37 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:37 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002918'}
2025-07-01 10:20:37 - core.http_client - DEBUG - 股票 002918 数据获取成功
2025-07-01 10:20:37 - core.http_client - INFO - 获取股票数据: 600347, 日期: 20250630
2025-07-01 10:20:38 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:38 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600347'}
2025-07-01 10:20:38 - core.http_client - DEBUG - 股票 600347 数据获取成功
2025-07-01 10:20:38 - core.http_client - INFO - 获取股票数据: 603218, 日期: 20250630
2025-07-01 10:20:39 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:39 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '603218'}
2025-07-01 10:20:39 - core.http_client - DEBUG - 股票 603218 数据获取成功
2025-07-01 10:20:39 - core.http_client - INFO - 获取股票数据: 600958, 日期: 20250630
2025-07-01 10:20:40 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:40 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600958'}
2025-07-01 10:20:40 - core.http_client - DEBUG - 股票 600958 数据获取成功
2025-07-01 10:20:40 - core.http_client - INFO - 获取股票数据: 300859, 日期: 20250630
2025-07-01 10:20:41 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:41 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300859'}
2025-07-01 10:20:41 - core.http_client - DEBUG - 股票 300859 数据获取成功
2025-07-01 10:20:41 - core.http_client - INFO - 获取股票数据: 301703, 日期: 20250630
2025-07-01 10:20:43 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:43 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301703'}
2025-07-01 10:20:43 - core.http_client - DEBUG - 股票 301703 数据获取成功
2025-07-01 10:20:43 - core.http_client - INFO - 获取股票数据: 600256, 日期: 20250630
2025-07-01 10:20:44 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:44 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600256'}
2025-07-01 10:20:44 - core.http_client - DEBUG - 股票 600256 数据获取成功
2025-07-01 10:20:44 - core.http_client - INFO - 获取股票数据: 602991, 日期: 20250630
2025-07-01 10:20:45 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:45 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '602991'}
2025-07-01 10:20:45 - core.http_client - DEBUG - 股票 602991 数据获取成功
2025-07-01 10:20:45 - core.http_client - INFO - 获取股票数据: 002119, 日期: 20250630
2025-07-01 10:20:46 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:46 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002119'}
2025-07-01 10:20:46 - core.http_client - DEBUG - 股票 002119 数据获取成功
2025-07-01 10:20:46 - core.http_client - INFO - 获取股票数据: 601336, 日期: 20250630
2025-07-01 10:20:47 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:47 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601336'}
2025-07-01 10:20:47 - core.http_client - DEBUG - 股票 601336 数据获取成功
2025-07-01 10:20:47 - core.http_client - INFO - 获取股票数据: 301684, 日期: 20250630
2025-07-01 10:20:48 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:48 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301684'}
2025-07-01 10:20:48 - core.http_client - DEBUG - 股票 301684 数据获取成功
2025-07-01 10:20:49 - core.http_client - INFO - 获取股票数据: 602335, 日期: 20250630
2025-07-01 10:20:50 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:50 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '602335'}
2025-07-01 10:20:50 - core.http_client - DEBUG - 股票 602335 数据获取成功
2025-07-01 10:20:50 - core.http_client - INFO - 获取股票数据: 602544, 日期: 20250630
2025-07-01 10:20:51 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:51 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '602544'}
2025-07-01 10:20:51 - core.http_client - DEBUG - 股票 602544 数据获取成功
2025-07-01 10:20:51 - core.http_client - INFO - 获取股票数据: 603486, 日期: 20250630
2025-07-01 10:20:52 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:52 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '603486'}
2025-07-01 10:20:52 - core.http_client - DEBUG - 股票 603486 数据获取成功
2025-07-01 10:20:52 - core.http_client - INFO - 获取股票数据: 002871, 日期: 20250630
2025-07-01 10:20:53 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:53 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002871'}
2025-07-01 10:20:53 - core.http_client - DEBUG - 股票 002871 数据获取成功
2025-07-01 10:20:53 - core.http_client - INFO - 获取股票数据: 002733, 日期: 20250630
2025-07-01 10:20:54 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:54 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002733'}
2025-07-01 10:20:54 - core.http_client - DEBUG - 股票 002733 数据获取成功
2025-07-01 10:20:55 - core.http_client - INFO - 获取股票数据: 300436, 日期: 20250630
2025-07-01 10:20:57 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:57 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300436'}
2025-07-01 10:20:57 - core.http_client - DEBUG - 股票 300436 数据获取成功
2025-07-01 10:20:57 - core.http_client - INFO - 获取股票数据: 300295, 日期: 20250630
2025-07-01 10:20:58 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:58 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300295'}
2025-07-01 10:20:58 - core.http_client - DEBUG - 股票 300295 数据获取成功
2025-07-01 10:20:58 - core.http_client - INFO - 获取股票数据: 688184, 日期: 20250630
2025-07-01 10:20:59 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:20:59 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688184'}
2025-07-01 10:20:59 - core.http_client - DEBUG - 股票 688184 数据获取成功
2025-07-01 10:20:59 - core.http_client - INFO - 获取股票数据: 600763, 日期: 20250630
2025-07-01 10:21:00 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:00 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600763'}
2025-07-01 10:21:00 - core.http_client - DEBUG - 股票 600763 数据获取成功
2025-07-01 10:21:00 - core.http_client - INFO - 获取股票数据: 000260, 日期: 20250630
2025-07-01 10:21:01 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:01 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000260'}
2025-07-01 10:21:01 - core.http_client - DEBUG - 股票 000260 数据获取成功
2025-07-01 10:21:01 - core.http_client - INFO - 获取股票数据: 002650, 日期: 20250630
2025-07-01 10:21:02 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:02 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002650'}
2025-07-01 10:21:02 - core.http_client - DEBUG - 股票 002650 数据获取成功
2025-07-01 10:21:03 - core.http_client - INFO - 获取股票数据: 300501, 日期: 20250630
2025-07-01 10:21:04 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:04 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300501'}
2025-07-01 10:21:04 - core.http_client - DEBUG - 股票 300501 数据获取成功
2025-07-01 10:21:04 - core.http_client - INFO - 获取股票数据: 601181, 日期: 20250630
2025-07-01 10:21:05 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:05 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601181'}
2025-07-01 10:21:05 - core.http_client - DEBUG - 股票 601181 数据获取成功
2025-07-01 10:21:05 - core.http_client - INFO - 获取股票数据: 688007, 日期: 20250630
2025-07-01 10:21:06 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:06 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688007'}
2025-07-01 10:21:06 - core.http_client - DEBUG - 股票 688007 数据获取成功
2025-07-01 10:21:06 - core.http_client - INFO - 获取股票数据: 688983, 日期: 20250630
2025-07-01 10:21:07 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:07 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688983'}
2025-07-01 10:21:07 - core.http_client - DEBUG - 股票 688983 数据获取成功
2025-07-01 10:21:07 - core.http_client - INFO - 获取股票数据: 600631, 日期: 20250630
2025-07-01 10:21:08 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:08 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600631'}
2025-07-01 10:21:08 - core.http_client - DEBUG - 股票 600631 数据获取成功
2025-07-01 10:21:08 - core.http_client - INFO - 获取股票数据: 300946, 日期: 20250630
2025-07-01 10:21:09 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:09 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300946'}
2025-07-01 10:21:09 - core.http_client - DEBUG - 股票 300946 数据获取成功
2025-07-01 10:21:10 - core.http_client - INFO - 获取股票数据: 000019, 日期: 20250630
2025-07-01 10:21:11 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:11 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000019'}
2025-07-01 10:21:11 - core.http_client - DEBUG - 股票 000019 数据获取成功
2025-07-01 10:21:11 - core.http_client - INFO - 获取股票数据: 000786, 日期: 20250630
2025-07-01 10:21:12 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:12 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000786'}
2025-07-01 10:21:12 - core.http_client - DEBUG - 股票 000786 数据获取成功
2025-07-01 10:21:12 - core.http_client - INFO - 获取股票数据: 602601, 日期: 20250630
2025-07-01 10:21:13 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:13 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '602601'}
2025-07-01 10:21:13 - core.http_client - DEBUG - 股票 602601 数据获取成功
2025-07-01 10:21:13 - core.http_client - INFO - 获取股票数据: 600904, 日期: 20250630
2025-07-01 10:21:14 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:14 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600904'}
2025-07-01 10:21:14 - core.http_client - DEBUG - 股票 600904 数据获取成功
2025-07-01 10:21:14 - core.http_client - INFO - 获取股票数据: 603089, 日期: 20250630
2025-07-01 10:21:15 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:15 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '603089'}
2025-07-01 10:21:15 - core.http_client - DEBUG - 股票 603089 数据获取成功
2025-07-01 10:21:15 - core.http_client - INFO - 获取股票数据: 601820, 日期: 20250630
2025-07-01 10:21:17 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:17 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601820'}
2025-07-01 10:21:17 - core.http_client - DEBUG - 股票 601820 数据获取成功
2025-07-01 10:21:17 - core.http_client - INFO - 获取股票数据: 000326, 日期: 20250630
2025-07-01 10:21:18 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:18 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000326'}
2025-07-01 10:21:18 - core.http_client - DEBUG - 股票 000326 数据获取成功
2025-07-01 10:21:18 - core.http_client - INFO - 获取股票数据: 688231, 日期: 20250630
2025-07-01 10:21:19 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:19 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688231'}
2025-07-01 10:21:19 - core.http_client - DEBUG - 股票 688231 数据获取成功
2025-07-01 10:21:20 - core.http_client - INFO - 获取股票数据: 602448, 日期: 20250630
2025-07-01 10:21:21 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:21 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '602448'}
2025-07-01 10:21:21 - core.http_client - DEBUG - 股票 602448 数据获取成功
2025-07-01 10:21:21 - core.http_client - INFO - 获取股票数据: 688541, 日期: 20250630
2025-07-01 10:21:22 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:22 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688541'}
2025-07-01 10:21:22 - core.http_client - DEBUG - 股票 688541 数据获取成功
2025-07-01 10:21:22 - core.http_client - INFO - 获取股票数据: 000022, 日期: 20250630
2025-07-01 10:21:23 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:23 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000022'}
2025-07-01 10:21:23 - core.http_client - DEBUG - 股票 000022 数据获取成功
2025-07-01 10:21:23 - core.http_client - INFO - 获取股票数据: 602230, 日期: 20250630
2025-07-01 10:21:24 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:24 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '602230'}
2025-07-01 10:21:24 - core.http_client - DEBUG - 股票 602230 数据获取成功
2025-07-01 10:21:25 - core.http_client - INFO - 获取股票数据: 002003, 日期: 20250630
2025-07-01 10:21:26 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:26 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002003'}
2025-07-01 10:21:26 - core.http_client - DEBUG - 股票 002003 数据获取成功
2025-07-01 10:21:26 - core.http_client - INFO - 获取股票数据: 301379, 日期: 20250630
2025-07-01 10:21:27 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:27 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301379'}
2025-07-01 10:21:27 - core.http_client - DEBUG - 股票 301379 数据获取成功
2025-07-01 10:21:27 - core.http_client - INFO - 获取股票数据: 002468, 日期: 20250630
2025-07-01 10:21:28 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:28 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002468'}
2025-07-01 10:21:28 - core.http_client - DEBUG - 股票 002468 数据获取成功
2025-07-01 10:21:28 - core.http_client - INFO - 获取股票数据: 602208, 日期: 20250630
2025-07-01 10:21:29 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:29 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '602208'}
2025-07-01 10:21:29 - core.http_client - DEBUG - 股票 602208 数据获取成功
2025-07-01 10:21:29 - core.http_client - INFO - 获取股票数据: 600726, 日期: 20250630
2025-07-01 10:21:30 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:30 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600726'}
2025-07-01 10:21:30 - core.http_client - DEBUG - 股票 600726 数据获取成功
2025-07-01 10:21:30 - core.http_client - INFO - 获取股票数据: 301440, 日期: 20250630
2025-07-01 10:21:32 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:32 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301440'}
2025-07-01 10:21:32 - core.http_client - DEBUG - 股票 301440 数据获取成功
2025-07-01 10:21:32 - core.http_client - INFO - 获取股票数据: 601287, 日期: 20250630
2025-07-01 10:21:33 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:33 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '601287'}
2025-07-01 10:21:33 - core.http_client - DEBUG - 股票 601287 数据获取成功
2025-07-01 10:21:33 - core.http_client - INFO - 获取股票数据: 600374, 日期: 20250630
2025-07-01 10:21:34 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:34 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600374'}
2025-07-01 10:21:34 - core.http_client - DEBUG - 股票 600374 数据获取成功
2025-07-01 10:21:34 - core.http_client - INFO - 获取股票数据: 600230, 日期: 20250630
2025-07-01 10:21:35 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:21:35 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600230'}
2025-07-01 10:21:35 - core.http_client - DEBUG - 股票 600230 数据获取成功
2025-07-01 10:21:35 - core.http_client - INFO - 获取股票数据: 000407, 日期: 20250630
2025-07-01 10:21:36 - core.http_client - INFO - HTTP会话已关闭
2025-07-01 10:23:47 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-07-01 10:23:49 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:23:49 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000001'}
2025-07-01 10:23:49 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-07-01 10:23:49 - core.http_client - INFO - 获取股票数据: 000002, 日期: 20250630
2025-07-01 10:23:50 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:23:50 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000002'}
2025-07-01 10:23:50 - core.http_client - DEBUG - 股票 000002 数据获取成功
2025-07-01 10:23:50 - core.http_client - INFO - 获取股票数据: 600000, 日期: 20250630
2025-07-01 10:23:51 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:23:51 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600000'}
2025-07-01 10:23:51 - core.http_client - DEBUG - 股票 600000 数据获取成功
2025-07-01 10:23:51 - core.http_client - INFO - 获取股票数据: 600036, 日期: 20250630
2025-07-01 10:23:53 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:23:53 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600036'}
2025-07-01 10:23:53 - core.http_client - DEBUG - 股票 600036 数据获取成功
2025-07-01 10:23:53 - core.http_client - INFO - 获取股票数据: 600519, 日期: 20250630
2025-07-01 10:23:54 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:23:54 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600519'}
2025-07-01 10:23:54 - core.http_client - DEBUG - 股票 600519 数据获取成功
2025-07-01 10:23:54 - core.http_client - INFO - 获取股票数据: 000858, 日期: 20250630
2025-07-01 10:23:55 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:23:55 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000858'}
2025-07-01 10:23:55 - core.http_client - DEBUG - 股票 000858 数据获取成功
2025-07-01 10:23:56 - core.http_client - INFO - 获取股票数据: 002415, 日期: 20250630
2025-07-01 10:23:57 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:23:57 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002415'}
2025-07-01 10:23:57 - core.http_client - DEBUG - 股票 002415 数据获取成功
2025-07-01 10:23:57 - core.http_client - INFO - 获取股票数据: 300059, 日期: 20250630
2025-07-01 10:23:58 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:23:58 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300059'}
2025-07-01 10:23:58 - core.http_client - DEBUG - 股票 300059 数据获取成功
2025-07-01 10:23:58 - core.http_client - INFO - HTTP会话已关闭
2025-07-01 10:23:58 - core.http_client - INFO - HTTP会话已关闭
2025-07-01 10:29:18 - core.http_client - INFO - 获取股票排行榜，起始索引: 0, 数量: 50
2025-07-01 10:29:20 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:29:20 - utils.logger - DEBUG - 请求参数: {'c': 'StockRanking', 'a': 'RealRankingInfo', 'Date': '', 'RStart': '', 'REnd': '', 'Ratio': '5', 'Type': '6', 'Order': '0', 'index': '0', 'st': '50', 'UserID': 710743, 'Token': '********************************'}
2025-07-01 10:29:20 - core.http_client - DEBUG - 股票排行榜数据获取成功
2025-07-01 10:29:20 - core.http_client - INFO - 获取股票排行榜，起始索引: 0, 数量: 500
2025-07-01 10:29:21 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:29:21 - utils.logger - DEBUG - 请求参数: {'c': 'StockRanking', 'a': 'RealRankingInfo', 'Date': '', 'RStart': '', 'REnd': '', 'Ratio': '5', 'Type': '6', 'Order': '0', 'index': '0', 'st': '500', 'UserID': 710743, 'Token': '********************************'}
2025-07-01 10:29:21 - core.http_client - DEBUG - 股票排行榜数据获取成功
2025-07-01 10:29:21 - core.http_client - INFO - 获取股票排行榜，起始索引: 0, 数量: 5000
2025-07-01 10:29:22 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:29:22 - utils.logger - DEBUG - 请求参数: {'c': 'StockRanking', 'a': 'RealRankingInfo', 'Date': '', 'RStart': '', 'REnd': '', 'Ratio': '5', 'Type': '6', 'Order': '0', 'index': '0', 'st': '5000', 'UserID': 710743, 'Token': '********************************'}
2025-07-01 10:29:22 - core.http_client - DEBUG - 股票排行榜数据获取成功
2025-07-01 10:29:22 - core.http_client - INFO - HTTP会话已关闭
2025-07-01 10:31:52 - core.http_client - INFO - 获取股票排行榜，起始索引: 0, 数量: 50
2025-07-01 10:31:54 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:31:54 - utils.logger - DEBUG - 请求参数: {'c': 'StockRanking', 'a': 'RealRankingInfo', 'Date': '', 'RStart': '', 'REnd': '', 'Ratio': '5', 'Type': '6', 'Order': '0', 'index': '0', 'st': '50', 'UserID': 710743, 'Token': '********************************'}
2025-07-01 10:31:54 - core.http_client - DEBUG - 股票排行榜数据获取成功
2025-07-01 10:31:54 - core.http_client - INFO - 获取股票排行榜，起始索引: 0, 数量: 500
2025-07-01 10:31:55 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:31:55 - utils.logger - DEBUG - 请求参数: {'c': 'StockRanking', 'a': 'RealRankingInfo', 'Date': '', 'RStart': '', 'REnd': '', 'Ratio': '5', 'Type': '6', 'Order': '0', 'index': '0', 'st': '500', 'UserID': 710743, 'Token': '********************************'}
2025-07-01 10:31:55 - core.http_client - DEBUG - 股票排行榜数据获取成功
2025-07-01 10:31:55 - core.http_client - INFO - 获取股票排行榜，起始索引: 0, 数量: 5000
2025-07-01 10:31:56 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:31:56 - utils.logger - DEBUG - 请求参数: {'c': 'StockRanking', 'a': 'RealRankingInfo', 'Date': '', 'RStart': '', 'REnd': '', 'Ratio': '5', 'Type': '6', 'Order': '0', 'index': '0', 'st': '5000', 'UserID': 710743, 'Token': '********************************'}
2025-07-01 10:31:56 - core.http_client - DEBUG - 股票排行榜数据获取成功
2025-07-01 10:31:56 - core.http_client - INFO - HTTP会话已关闭
2025-07-01 10:33:08 - core.http_client - INFO - 获取股票排行榜，起始索引: 0, 数量: 5000
2025-07-01 10:33:10 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:33:10 - utils.logger - DEBUG - 请求参数: {'c': 'StockRanking', 'a': 'RealRankingInfo', 'Date': '', 'RStart': '', 'REnd': '', 'Ratio': '5', 'Type': '6', 'Order': '0', 'index': '0', 'st': '5000', 'UserID': 710743, 'Token': '********************************'}
2025-07-01 10:33:10 - core.http_client - DEBUG - 股票排行榜数据获取成功
2025-07-01 10:33:10 - core.http_client - INFO - HTTP会话已关闭
2025-07-01 10:39:26 - utils.logger - INFO - 调用函数: get_stock_list_from_tushare
2025-07-01 10:39:26 - core.data_collector - INFO - 开始获取完整股票列表
2025-07-01 10:39:26 - core.http_client - INFO - 获取股票排行榜，起始索引: 0, 数量: 5000
2025-07-01 10:39:27 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:39:27 - utils.logger - DEBUG - 请求参数: {'c': 'StockRanking', 'a': 'RealRankingInfo', 'Date': '', 'RStart': '', 'REnd': '', 'Ratio': '5', 'Type': '6', 'Order': '0', 'index': '0', 'st': '5000', 'UserID': 710743, 'Token': '********************************'}
2025-07-01 10:39:27 - core.http_client - DEBUG - 股票排行榜数据获取成功
2025-07-01 10:39:27 - core.data_collector - INFO - 从开盘啦API获取到 5000 只股票
2025-07-01 10:39:27 - utils.logger - INFO - 函数 get_stock_list_from_tushare 执行成功
2025-07-01 10:39:27 - core.http_client - INFO - HTTP会话已关闭
2025-07-01 10:59:32 - __main__ - INFO - 开始采集今日数据: 20250701
2025-07-01 10:59:32 - utils.logger - INFO - 调用函数: get_stock_list_from_tushare
2025-07-01 10:59:32 - core.data_collector - INFO - 开始获取完整股票列表
2025-07-01 10:59:32 - core.http_client - INFO - 获取股票排行榜，起始索引: 0, 数量: 5000
2025-07-01 10:59:34 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:59:34 - utils.logger - DEBUG - 请求参数: {'c': 'StockRanking', 'a': 'RealRankingInfo', 'Date': '', 'RStart': '', 'REnd': '', 'Ratio': '5', 'Type': '6', 'Order': '0', 'index': '0', 'st': '5000', 'UserID': 710743, 'Token': '********************************'}
2025-07-01 10:59:34 - core.http_client - DEBUG - 股票排行榜数据获取成功
2025-07-01 10:59:34 - core.data_collector - INFO - 从开盘啦API获取到 5000 只股票
2025-07-01 10:59:34 - utils.logger - INFO - 函数 get_stock_list_from_tushare 执行成功
2025-07-01 10:59:34 - __main__ - INFO - 获取到 5000 只股票
2025-07-01 10:59:34 - utils.logger - INFO - 调用函数: batch_collect_stocks
2025-07-01 10:59:34 - core.data_collector - INFO - 开始批量采集 10 只股票数据
2025-07-01 10:59:34 - utils.logger - INFO - 调用函数: collect_stock_data
2025-07-01 10:59:34 - utils.logger - INFO - 调用函数: collect_stock_data
2025-07-01 10:59:34 - core.http_client - INFO - 获取股票数据: 301024, 日期: 20250701
2025-07-01 10:59:34 - utils.logger - INFO - 调用函数: collect_stock_data
2025-07-01 10:59:34 - utils.logger - INFO - 调用函数: collect_stock_data
2025-07-01 10:59:34 - core.http_client - INFO - 获取股票数据: 300801, 日期: 20250701
2025-07-01 10:59:34 - utils.logger - INFO - 调用函数: collect_stock_data
2025-07-01 10:59:34 - core.http_client - INFO - 获取股票数据: 002846, 日期: 20250701
2025-07-01 10:59:34 - core.http_client - INFO - 获取股票数据: 001236, 日期: 20250701
2025-07-01 10:59:34 - core.http_client - INFO - 获取股票数据: 300340, 日期: 20250701
2025-07-01 10:59:35 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:59:35 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301024'}
2025-07-01 10:59:35 - core.http_client - DEBUG - 股票 301024 数据获取成功
2025-07-01 10:59:35 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:59:35 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300340'}
2025-07-01 10:59:35 - core.http_client - DEBUG - 股票 300340 数据获取成功
2025-07-01 10:59:35 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:59:35 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002846'}
2025-07-01 10:59:35 - core.http_client - DEBUG - 股票 002846 数据获取成功
2025-07-01 10:59:35 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:59:35 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300801'}
2025-07-01 10:59:35 - core.http_client - DEBUG - 股票 300801 数据获取成功
2025-07-01 10:59:35 - core.data_collector - DEBUG - 股票 301024 数据保存成功
2025-07-01 10:59:35 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-07-01 10:59:35 - utils.logger - INFO - 调用函数: collect_stock_data
2025-07-01 10:59:35 - core.http_client - INFO - 获取股票数据: 002987, 日期: 20250701
2025-07-01 10:59:35 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:59:35 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '001236'}
2025-07-01 10:59:35 - core.http_client - DEBUG - 股票 001236 数据获取成功
2025-07-01 10:59:35 - core.data_collector - DEBUG - 股票 300340 数据保存成功
2025-07-01 10:59:35 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-07-01 10:59:35 - utils.logger - INFO - 调用函数: collect_stock_data
2025-07-01 10:59:35 - core.http_client - INFO - 获取股票数据: 688787, 日期: 20250701
2025-07-01 10:59:35 - core.data_collector - DEBUG - 股票 300801 数据保存成功
2025-07-01 10:59:35 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-07-01 10:59:35 - utils.logger - INFO - 调用函数: collect_stock_data
2025-07-01 10:59:35 - core.http_client - INFO - 获取股票数据: 002878, 日期: 20250701
2025-07-01 10:59:35 - core.data_collector - DEBUG - 股票 001236 数据保存成功
2025-07-01 10:59:35 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-07-01 10:59:35 - utils.logger - INFO - 调用函数: collect_stock_data
2025-07-01 10:59:35 - core.http_client - INFO - 获取股票数据: 603093, 日期: 20250701
2025-07-01 10:59:35 - core.data_collector - DEBUG - 股票 002846 数据保存成功
2025-07-01 10:59:35 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-07-01 10:59:35 - utils.logger - INFO - 调用函数: collect_stock_data
2025-07-01 10:59:35 - core.http_client - INFO - 获取股票数据: 300468, 日期: 20250701
2025-07-01 10:59:36 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:59:36 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002987'}
2025-07-01 10:59:36 - core.http_client - DEBUG - 股票 002987 数据获取成功
2025-07-01 10:59:36 - core.data_collector - DEBUG - 股票 002987 数据保存成功
2025-07-01 10:59:36 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-07-01 10:59:36 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:59:36 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688787'}
2025-07-01 10:59:36 - core.http_client - DEBUG - 股票 688787 数据获取成功
2025-07-01 10:59:36 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:59:36 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002878'}
2025-07-01 10:59:36 - core.http_client - DEBUG - 股票 002878 数据获取成功
2025-07-01 10:59:36 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:59:36 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '603093'}
2025-07-01 10:59:36 - core.http_client - DEBUG - 股票 603093 数据获取成功
2025-07-01 10:59:36 - core.data_collector - DEBUG - 股票 688787 数据保存成功
2025-07-01 10:59:36 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-07-01 10:59:36 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:59:36 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300468'}
2025-07-01 10:59:36 - core.http_client - DEBUG - 股票 300468 数据获取成功
2025-07-01 10:59:36 - core.data_collector - DEBUG - 股票 603093 数据保存成功
2025-07-01 10:59:36 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-07-01 10:59:36 - core.data_collector - DEBUG - 股票 300468 数据保存成功
2025-07-01 10:59:36 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-07-01 10:59:36 - core.data_collector - DEBUG - 股票 002878 数据保存成功
2025-07-01 10:59:36 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-07-01 10:59:36 - core.data_collector - INFO - 批量采集完成: 成功 10, 失败 0
2025-07-01 10:59:36 - utils.logger - INFO - 函数 batch_collect_stocks 执行成功
2025-07-01 10:59:36 - __main__ - INFO - 股票数据采集结果: {'success': 10, 'failed': 0, 'total': 10}
2025-07-01 10:59:36 - utils.logger - INFO - 调用函数: collect_plate_data
2025-07-01 10:59:36 - core.data_collector - INFO - 开始采集板块数据，日期: 20250701
2025-07-01 10:59:36 - core.http_client - INFO - 获取板块数据，日期: 2025-07-01
2025-07-01 10:59:37 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:59:37 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'Date': '2025-07-01', 'PType': '2', 'POrder': '1', 'ZSType': '5', 'rate': '1', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '20', 'UserID': 710743, 'Token': '********************************'}
2025-07-01 10:59:37 - core.http_client - DEBUG - 板块数据获取成功
2025-07-01 10:59:37 - core.data_collector - INFO - 解析了 20 个板块数据
2025-07-01 10:59:37 - core.data_collector - INFO - 板块数据保存成功
2025-07-01 10:59:37 - utils.logger - INFO - 函数 collect_plate_data 执行成功
2025-07-01 10:59:37 - __main__ - INFO - 板块数据采集: 成功
2025-07-01 10:59:37 - utils.logger - INFO - 调用函数: collect_longhubang_data
2025-07-01 10:59:37 - core.data_collector - INFO - 开始采集龙虎榜数据，日期: 20250701
2025-07-01 10:59:37 - core.http_client - INFO - 获取龙虎榜数据，日期: 20250701
2025-07-01 10:59:39 - utils.logger - INFO - API请求成功 - URL: https://pclhb.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 10:59:39 - utils.logger - DEBUG - 请求参数: {'c': 'LongHuBang', 'a': 'GetStockList', 'Index': '0', 'st': '300', 'Time': '', 'UserID': 710743, 'Token': '********************************'}
2025-07-01 10:59:39 - core.http_client - DEBUG - 龙虎榜数据获取成功
2025-07-01 10:59:39 - core.data_collector - INFO - 龙虎榜数据保存成功
2025-07-01 10:59:39 - utils.logger - INFO - 函数 collect_longhubang_data 执行成功
2025-07-01 10:59:39 - __main__ - INFO - 龙虎榜数据采集: 成功
2025-07-01 10:59:39 - core.http_client - INFO - HTTP会话已关闭
2025-07-01 11:04:34 - __main__ - INFO - 开始采集今日数据: 20250701
2025-07-01 11:04:34 - utils.logger - INFO - 调用函数: get_stock_list_from_tushare
2025-07-01 11:04:34 - core.data_collector - INFO - 开始获取完整股票列表
2025-07-01 11:04:34 - core.http_client - INFO - 获取股票排行榜，起始索引: 0, 数量: 5000
2025-07-01 11:04:36 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 11:04:36 - utils.logger - DEBUG - 请求参数: {'c': 'StockRanking', 'a': 'RealRankingInfo', 'Date': '', 'RStart': '', 'REnd': '', 'Ratio': '5', 'Type': '6', 'Order': '0', 'index': '0', 'st': '5000', 'UserID': 710743, 'Token': '********************************'}
2025-07-01 11:04:36 - core.http_client - DEBUG - 股票排行榜数据获取成功
2025-07-01 11:04:36 - core.data_collector - INFO - 从开盘啦API获取到 5000 只股票
2025-07-01 11:04:36 - utils.logger - INFO - 函数 get_stock_list_from_tushare 执行成功
2025-07-01 11:04:36 - __main__ - INFO - 获取到 5000 只股票
2025-07-01 11:04:36 - utils.logger - INFO - 调用函数: batch_collect_stocks
2025-07-01 11:04:36 - core.data_collector - INFO - 开始批量采集 10 只股票数据
2025-07-01 11:04:36 - utils.logger - INFO - 调用函数: collect_stock_data
2025-07-01 11:04:36 - core.http_client - INFO - 获取股票数据: 301024, 日期: 20250701
2025-07-01 11:04:36 - utils.logger - INFO - 调用函数: collect_stock_data
2025-07-01 11:04:36 - core.http_client - INFO - 获取股票数据: 002846, 日期: 20250701
2025-07-01 11:04:36 - utils.logger - INFO - 调用函数: collect_stock_data
2025-07-01 11:04:36 - core.http_client - INFO - 获取股票数据: 001236, 日期: 20250701
2025-07-01 11:04:36 - utils.logger - INFO - 调用函数: collect_stock_data
2025-07-01 11:04:36 - utils.logger - INFO - 调用函数: collect_stock_data
2025-07-01 11:04:36 - core.http_client - INFO - 获取股票数据: 300801, 日期: 20250701
2025-07-01 11:04:36 - core.http_client - INFO - 获取股票数据: 300340, 日期: 20250701
2025-07-01 11:04:37 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 11:04:37 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301024'}
2025-07-01 11:04:37 - core.http_client - DEBUG - 股票 301024 数据获取成功
2025-07-01 11:04:37 - core.data_collector - DEBUG - 股票 301024 数据保存成功
2025-07-01 11:04:37 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-07-01 11:04:37 - utils.logger - INFO - 调用函数: collect_stock_data
2025-07-01 11:04:37 - core.http_client - INFO - 获取股票数据: 002987, 日期: 20250701
2025-07-01 11:04:37 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 11:04:37 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300801'}
2025-07-01 11:04:37 - core.http_client - DEBUG - 股票 300801 数据获取成功
2025-07-01 11:04:37 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 11:04:37 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '001236'}
2025-07-01 11:04:37 - core.http_client - DEBUG - 股票 001236 数据获取成功
2025-07-01 11:04:37 - core.data_collector - DEBUG - 股票 300801 数据保存成功
2025-07-01 11:04:37 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-07-01 11:04:37 - utils.logger - INFO - 调用函数: collect_stock_data
2025-07-01 11:04:37 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 11:04:37 - core.http_client - INFO - 获取股票数据: 688787, 日期: 20250701
2025-07-01 11:04:37 - core.data_collector - DEBUG - 股票 001236 数据保存成功
2025-07-01 11:04:37 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-07-01 11:04:37 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002846'}
2025-07-01 11:04:37 - core.http_client - DEBUG - 股票 002846 数据获取成功
2025-07-01 11:04:37 - utils.logger - INFO - 调用函数: collect_stock_data
2025-07-01 11:04:37 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 11:04:37 - core.http_client - INFO - 获取股票数据: 600666, 日期: 20250701
2025-07-01 11:04:37 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300340'}
2025-07-01 11:04:37 - core.http_client - DEBUG - 股票 300340 数据获取成功
2025-07-01 11:04:37 - core.data_collector - DEBUG - 股票 002846 数据保存成功
2025-07-01 11:04:37 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-07-01 11:04:37 - utils.logger - INFO - 调用函数: collect_stock_data
2025-07-01 11:04:37 - core.http_client - INFO - 获取股票数据: 300468, 日期: 20250701
2025-07-01 11:04:37 - core.data_collector - DEBUG - 股票 300340 数据保存成功
2025-07-01 11:04:37 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-07-01 11:04:37 - utils.logger - INFO - 调用函数: collect_stock_data
2025-07-01 11:04:37 - core.http_client - INFO - 获取股票数据: 002878, 日期: 20250701
2025-07-01 11:04:38 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 11:04:38 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002987'}
2025-07-01 11:04:38 - core.http_client - DEBUG - 股票 002987 数据获取成功
2025-07-01 11:04:38 - core.data_collector - DEBUG - 股票 002987 数据保存成功
2025-07-01 11:04:38 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-07-01 11:04:38 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 11:04:38 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688787'}
2025-07-01 11:04:38 - core.http_client - DEBUG - 股票 688787 数据获取成功
2025-07-01 11:04:38 - core.data_collector - DEBUG - 股票 688787 数据保存成功
2025-07-01 11:04:38 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-07-01 11:04:38 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 11:04:38 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600666'}
2025-07-01 11:04:38 - core.http_client - DEBUG - 股票 600666 数据获取成功
2025-07-01 11:04:38 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 11:04:38 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300468'}
2025-07-01 11:04:38 - core.http_client - DEBUG - 股票 300468 数据获取成功
2025-07-01 11:04:38 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 11:04:38 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002878'}
2025-07-01 11:04:38 - core.http_client - DEBUG - 股票 002878 数据获取成功
2025-07-01 11:04:38 - core.data_collector - DEBUG - 股票 600666 数据保存成功
2025-07-01 11:04:38 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-07-01 11:04:38 - core.data_collector - DEBUG - 股票 300468 数据保存成功
2025-07-01 11:04:38 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-07-01 11:04:38 - core.data_collector - DEBUG - 股票 002878 数据保存成功
2025-07-01 11:04:38 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-07-01 11:04:38 - core.data_collector - INFO - 批量采集完成: 成功 10, 失败 0
2025-07-01 11:04:38 - utils.logger - INFO - 函数 batch_collect_stocks 执行成功
2025-07-01 11:04:38 - __main__ - INFO - 股票数据采集结果: {'success': 10, 'failed': 0, 'total': 10}
2025-07-01 11:04:38 - utils.logger - INFO - 调用函数: collect_plate_data
2025-07-01 11:04:38 - core.data_collector - INFO - 开始采集板块数据，日期: 20250701
2025-07-01 11:04:38 - core.http_client - INFO - 获取板块数据，日期: 2025-07-01
2025-07-01 11:04:39 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 11:04:39 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'Date': '2025-07-01', 'PType': '2', 'POrder': '1', 'ZSType': '5', 'rate': '1', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '20', 'UserID': 710743, 'Token': '********************************'}
2025-07-01 11:04:39 - core.http_client - DEBUG - 板块数据获取成功
2025-07-01 11:04:39 - core.data_collector - INFO - 解析了 20 个板块数据
2025-07-01 11:04:39 - core.data_collector - INFO - 板块数据保存成功
2025-07-01 11:04:39 - utils.logger - INFO - 函数 collect_plate_data 执行成功
2025-07-01 11:04:39 - __main__ - INFO - 板块数据采集: 成功
2025-07-01 11:04:39 - utils.logger - INFO - 调用函数: collect_longhubang_data
2025-07-01 11:04:39 - core.data_collector - INFO - 开始采集龙虎榜数据，日期: 20250701
2025-07-01 11:04:39 - core.http_client - INFO - 获取龙虎榜数据，日期: 20250701
2025-07-01 11:04:41 - utils.logger - INFO - API请求成功 - URL: https://pclhb.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 11:04:41 - utils.logger - DEBUG - 请求参数: {'c': 'LongHuBang', 'a': 'GetStockList', 'Index': '0', 'st': '300', 'Time': '', 'UserID': 710743, 'Token': '********************************'}
2025-07-01 11:04:41 - core.http_client - DEBUG - 龙虎榜数据获取成功
2025-07-01 11:04:41 - core.data_collector - INFO - 龙虎榜数据保存成功
2025-07-01 11:04:41 - utils.logger - INFO - 函数 collect_longhubang_data 执行成功
2025-07-01 11:04:41 - __main__ - INFO - 龙虎榜数据采集: 成功
2025-07-01 11:04:41 - core.http_client - INFO - HTTP会话已关闭
2025-07-01 11:07:36 - core.http_client - INFO - 获取股票数据: 301236, 日期: 20250701
2025-07-01 11:07:38 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 11:07:38 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '301236'}
2025-07-01 11:07:38 - core.http_client - DEBUG - 股票 301236 数据获取成功
2025-07-01 11:07:38 - core.http_client - INFO - 获取股票数据: 688652, 日期: 20250701
2025-07-01 11:07:39 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 11:07:39 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688652'}
2025-07-01 11:07:39 - core.http_client - DEBUG - 股票 688652 数据获取成功
2025-07-01 11:07:39 - core.http_client - INFO - 获取股票数据: 688459, 日期: 20250701
2025-07-01 11:07:40 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 11:07:40 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '688459'}
2025-07-01 11:07:40 - core.http_client - DEBUG - 股票 688459 数据获取成功
2025-07-01 11:07:40 - core.http_client - INFO - 获取股票数据: 603637, 日期: 20250701
2025-07-01 11:07:41 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 11:07:41 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '603637'}
2025-07-01 11:07:41 - core.http_client - DEBUG - 股票 603637 数据获取成功
2025-07-01 11:07:41 - core.http_client - INFO - 获取股票数据: 300331, 日期: 20250701
2025-07-01 11:07:42 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 11:07:42 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300331'}
2025-07-01 11:07:42 - core.http_client - DEBUG - 股票 300331 数据获取成功
2025-07-01 11:07:42 - core.http_client - INFO - 获取股票数据: 300877, 日期: 20250701
2025-07-01 11:07:43 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 11:07:43 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300877'}
2025-07-01 11:07:43 - core.http_client - DEBUG - 股票 300877 数据获取成功
2025-07-01 11:07:43 - core.http_client - INFO - 获取股票数据: 605167, 日期: 20250701
2025-07-01 11:07:44 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 11:07:44 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '605167'}
2025-07-01 11:07:44 - core.http_client - DEBUG - 股票 605167 数据获取成功
2025-07-01 11:07:44 - core.http_client - INFO - 获取股票数据: 002574, 日期: 20250701
2025-07-01 11:07:45 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 11:07:45 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002574'}
2025-07-01 11:07:45 - core.http_client - DEBUG - 股票 002574 数据获取成功
2025-07-01 11:07:45 - core.http_client - INFO - 获取股票数据: 002161, 日期: 20250701
2025-07-01 11:07:46 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 11:07:46 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002161'}
2025-07-01 11:07:46 - core.http_client - DEBUG - 股票 002161 数据获取成功
2025-07-01 11:07:46 - core.http_client - INFO - 获取股票数据: 831726, 日期: 20250701
2025-07-01 11:07:47 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-07-01 11:07:47 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250701', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '831726'}
2025-07-01 11:07:47 - core.http_client - DEBUG - 股票 831726 数据获取成功
2025-07-01 11:07:47 - core.http_client - INFO - HTTP会话已关闭
