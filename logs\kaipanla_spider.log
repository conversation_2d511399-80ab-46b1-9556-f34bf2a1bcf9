2025-06-30 19:11:37 - __main__ - INFO - 初始化数据库...
2025-06-30 19:11:38 - __main__ - INFO - 数据库初始化完成
2025-06-30 19:11:49 - __main__ - INFO - 测试开盘啦API连接...
2025-06-30 19:11:49 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 19:11:50 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:11:50 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '000001'}
2025-06-30 19:11:50 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 19:11:50 - core.http_client - INFO - API连接测试成功
2025-06-30 19:11:50 - __main__ - INFO - ✅ API连接测试成功
2025-06-30 19:12:03 - __main__ - INFO - === 开始基础功能测试 ===
2025-06-30 19:12:03 - __main__ - INFO - 1. 测试API连接...
2025-06-30 19:12:03 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 19:12:04 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:12:04 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '000001'}
2025-06-30 19:12:04 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 19:12:04 - core.http_client - INFO - API连接测试成功
2025-06-30 19:12:04 - __main__ - INFO -    API连接: ✅ 成功
2025-06-30 19:12:04 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 19:12:04 - __main__ - INFO - 2. 测试数据库...
2025-06-30 19:12:04 - __main__ - ERROR -    数据库连接异常: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
2025-06-30 19:12:04 - __main__ - INFO - 3. 测试数据采集...
2025-06-30 19:12:04 - utils.logger - INFO - 调用函数: get_stock_list_from_tushare
2025-06-30 19:12:04 - core.data_collector - INFO - 开始从Tushare获取股票列表
2025-06-30 19:12:04 - core.data_collector - INFO - 获取到 10 只股票
2025-06-30 19:12:04 - utils.logger - INFO - 函数 get_stock_list_from_tushare 执行成功
2025-06-30 19:12:04 - __main__ - INFO -    获取股票列表: ✅ 成功 (10 只)
2025-06-30 19:12:04 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 19:12:04 - __main__ - INFO - === 基础功能测试完成 ===
2025-06-30 19:12:53 - __main__ - INFO - === 开始基础功能测试 ===
2025-06-30 19:12:53 - __main__ - INFO - 1. 测试API连接...
2025-06-30 19:12:53 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 19:12:54 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:12:54 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '000001'}
2025-06-30 19:12:54 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 19:12:54 - core.http_client - INFO - API连接测试成功
2025-06-30 19:12:54 - __main__ - INFO -    API连接: ✅ 成功
2025-06-30 19:12:54 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 19:12:54 - __main__ - INFO - 2. 测试数据库...
2025-06-30 19:12:54 - __main__ - INFO -    数据库连接: ✅ 成功
2025-06-30 19:12:54 - __main__ - INFO - 3. 测试数据采集...
2025-06-30 19:12:54 - utils.logger - INFO - 调用函数: get_stock_list_from_tushare
2025-06-30 19:12:54 - core.data_collector - INFO - 开始从Tushare获取股票列表
2025-06-30 19:12:54 - core.data_collector - INFO - 获取到 10 只股票
2025-06-30 19:12:54 - utils.logger - INFO - 函数 get_stock_list_from_tushare 执行成功
2025-06-30 19:12:54 - __main__ - INFO -    获取股票列表: ✅ 成功 (10 只)
2025-06-30 19:12:54 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 19:12:54 - __main__ - INFO - === 基础功能测试完成 ===
2025-06-30 19:13:12 - __main__ - INFO - 开始采集今日数据: 20250630
2025-06-30 19:13:12 - utils.logger - INFO - 调用函数: get_stock_list_from_tushare
2025-06-30 19:13:12 - core.data_collector - INFO - 开始从Tushare获取股票列表
2025-06-30 19:13:12 - core.data_collector - INFO - 获取到 10 只股票
2025-06-30 19:13:12 - utils.logger - INFO - 函数 get_stock_list_from_tushare 执行成功
2025-06-30 19:13:12 - __main__ - INFO - 获取到 10 只股票
2025-06-30 19:13:12 - utils.logger - INFO - 调用函数: batch_collect_stocks
2025-06-30 19:13:12 - core.data_collector - INFO - 开始批量采集 10 只股票数据
2025-06-30 19:13:12 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:13:12 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 19:13:12 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:13:12 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:13:12 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:13:12 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:13:12 - core.http_client - INFO - 获取股票数据: 000002, 日期: 20250630
2025-06-30 19:13:12 - core.http_client - INFO - 获取股票数据: 000858, 日期: 20250630
2025-06-30 19:13:12 - core.http_client - INFO - 获取股票数据: 002415, 日期: 20250630
2025-06-30 19:13:12 - core.http_client - INFO - 获取股票数据: 600000, 日期: 20250630
2025-06-30 19:13:13 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:13:13 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:13:13 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '000002'}
2025-06-30 19:13:13 - core.http_client - DEBUG - 股票 000002 数据获取成功
2025-06-30 19:13:13 - core.data_collector - DEBUG - 股票 000002 数据保存成功
2025-06-30 19:13:13 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '000001'}
2025-06-30 19:13:13 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 19:13:13 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:13:13 - core.data_collector - DEBUG - 股票 000001 数据保存成功
2025-06-30 19:13:13 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:13:13 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:13:13 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:13:13 - core.http_client - INFO - 获取股票数据: 600036, 日期: 20250630
2025-06-30 19:13:13 - core.http_client - INFO - 获取股票数据: 600519, 日期: 20250630
2025-06-30 19:13:13 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:13:13 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '000858'}
2025-06-30 19:13:13 - core.http_client - DEBUG - 股票 000858 数据获取成功
2025-06-30 19:13:13 - core.data_collector - DEBUG - 股票 000858 数据保存成功
2025-06-30 19:13:13 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:13:13 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:13:13 - core.http_client - INFO - 获取股票数据: 000858, 日期: 20250630
2025-06-30 19:13:13 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:13:13 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '002415'}
2025-06-30 19:13:13 - core.http_client - DEBUG - 股票 002415 数据获取成功
2025-06-30 19:13:13 - core.data_collector - DEBUG - 股票 002415 数据保存成功
2025-06-30 19:13:13 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:13:13 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:13:13 - core.http_client - INFO - 获取股票数据: 002594, 日期: 20250630
2025-06-30 19:13:13 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:13:13 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '600000'}
2025-06-30 19:13:13 - core.http_client - DEBUG - 股票 600000 数据获取成功
2025-06-30 19:13:13 - core.data_collector - DEBUG - 股票 600000 数据保存成功
2025-06-30 19:13:13 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:13:13 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:13:13 - core.http_client - INFO - 获取股票数据: 300059, 日期: 20250630
2025-06-30 19:13:14 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:13:14 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '600036'}
2025-06-30 19:13:14 - core.http_client - DEBUG - 股票 600036 数据获取成功
2025-06-30 19:13:14 - core.data_collector - DEBUG - 股票 600036 数据保存成功
2025-06-30 19:13:14 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:13:14 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:13:14 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '600519'}
2025-06-30 19:13:14 - core.http_client - DEBUG - 股票 600519 数据获取成功
2025-06-30 19:13:14 - core.data_collector - DEBUG - 股票 600519 数据保存成功
2025-06-30 19:13:14 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:13:14 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:13:14 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '000858'}
2025-06-30 19:13:14 - core.http_client - DEBUG - 股票 000858 数据获取成功
2025-06-30 19:13:14 - core.data_collector - DEBUG - 股票 000858 数据保存成功
2025-06-30 19:13:14 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:13:14 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:13:14 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '002594'}
2025-06-30 19:13:14 - core.http_client - DEBUG - 股票 002594 数据获取成功
2025-06-30 19:13:14 - core.data_collector - DEBUG - 股票 002594 数据保存成功
2025-06-30 19:13:14 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:13:14 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:13:14 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '300059'}
2025-06-30 19:13:14 - core.http_client - DEBUG - 股票 300059 数据获取成功
2025-06-30 19:13:14 - core.data_collector - DEBUG - 股票 300059 数据保存成功
2025-06-30 19:13:14 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:13:14 - core.data_collector - INFO - 批量采集完成: 成功 10, 失败 0
2025-06-30 19:13:14 - utils.logger - INFO - 函数 batch_collect_stocks 执行成功
2025-06-30 19:13:14 - __main__ - INFO - 股票数据采集结果: {'success': 10, 'failed': 0, 'total': 10}
2025-06-30 19:13:14 - utils.logger - INFO - 调用函数: collect_plate_data
2025-06-30 19:13:14 - core.data_collector - INFO - 开始采集板块数据，日期: 20250630
2025-06-30 19:13:14 - core.http_client - INFO - 获取板块数据，日期: 20250630
2025-06-30 19:13:15 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:13:15 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetPlateList', 'Day': '20250630', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886'}
2025-06-30 19:13:15 - core.http_client - DEBUG - 板块数据获取成功
2025-06-30 19:13:15 - core.data_collector - INFO - 板块数据保存成功
2025-06-30 19:13:15 - utils.logger - INFO - 函数 collect_plate_data 执行成功
2025-06-30 19:13:15 - __main__ - INFO - 板块数据采集: 成功
2025-06-30 19:13:15 - utils.logger - INFO - 调用函数: collect_longhubang_data
2025-06-30 19:13:15 - core.data_collector - INFO - 开始采集龙虎榜数据，日期: 20250630
2025-06-30 19:13:15 - core.http_client - INFO - 获取龙虎榜数据，日期: 20250630
2025-06-30 19:13:17 - utils.logger - INFO - API请求成功 - URL: https://pclhb.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:13:17 - utils.logger - DEBUG - 请求参数: {'c': 'LongHuBang', 'a': 'GetLongHuBangData', 'Day': '20250630', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886'}
2025-06-30 19:13:17 - core.http_client - DEBUG - 龙虎榜数据获取成功
2025-06-30 19:13:17 - core.data_collector - INFO - 龙虎榜数据保存成功
2025-06-30 19:13:17 - utils.logger - INFO - 函数 collect_longhubang_data 执行成功
2025-06-30 19:13:17 - __main__ - INFO - 龙虎榜数据采集: 成功
2025-06-30 19:13:17 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 19:13:28 - __main__ - INFO - === 系统状态 ===
2025-06-30 19:13:28 - __main__ - INFO - 数据库URL: sqlite:///kaipanla_data.db
2025-06-30 19:13:28 - __main__ - INFO - 日志级别: DEBUG
2025-06-30 19:13:28 - __main__ - INFO - 错误统计: {'error_count': 0, 'warning_count': 0, 'errors': [], 'warnings': []}
2025-06-30 19:13:28 - utils.logger - INFO - 调用函数: get_data_statistics
2025-06-30 19:13:28 - core.data_processor - INFO - 数据统计完成: {'stock_quote_count': 0, 'stock_tag_count': 0, 'stock_concept_count': 0, 'plate_info_count': 0, 'long_hu_bang_count': 0}
2025-06-30 19:13:28 - utils.logger - INFO - 函数 get_data_statistics 执行成功
2025-06-30 19:13:29 - __main__ - INFO - 数据统计: {'stock_quote_count': 0, 'stock_tag_count': 0, 'stock_concept_count': 0, 'plate_info_count': 0, 'long_hu_bang_count': 0}
2025-06-30 19:13:54 - __main__ - INFO - 开始导出数据: 20250630
2025-06-30 19:13:54 - utils.logger - INFO - 调用函数: export_to_excel
2025-06-30 19:13:54 - core.data_processor - INFO - 开始导出数据到Excel: test_export.xlsx
2025-06-30 19:13:55 - core.data_processor - ERROR - 导出数据到Excel失败: At least one sheet must be visible
2025-06-30 19:13:55 - utils.logger - ERROR - 函数 export_to_excel 执行失败: At least one sheet must be visible
2025-06-30 19:13:55 - __main__ - ERROR - 数据导出失败: At least one sheet must be visible
2025-06-30 19:17:19 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 19:17:20 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:17:20 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886', 'StockID': '000001'}
2025-06-30 19:17:20 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 19:17:20 - core.http_client - INFO - 获取板块数据，日期: 20250630
2025-06-30 19:17:21 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:17:21 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetPlateList', 'Day': '20250630', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886'}
2025-06-30 19:17:21 - core.http_client - DEBUG - 板块数据获取成功
2025-06-30 19:17:21 - core.http_client - INFO - 获取龙虎榜数据，日期: 20250630
2025-06-30 19:17:23 - utils.logger - INFO - API请求成功 - URL: https://pclhb.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:17:23 - utils.logger - DEBUG - 请求参数: {'c': 'LongHuBang', 'a': 'GetLongHuBangData', 'Day': '20250630', 'UserID': 399083, 'Token': '71aef0e806e61ad3169ddc9473e37886'}
2025-06-30 19:17:23 - core.http_client - DEBUG - 龙虎榜数据获取成功
2025-06-30 19:17:23 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 19:29:41 - core.login_manager - WARNING - 凭据已失效
2025-06-30 19:29:41 - core.login_manager - INFO - 登录管理器会话已关闭
2025-06-30 19:35:44 - core.login_manager - INFO - 凭据测试成功
2025-06-30 19:35:44 - core.login_manager - INFO - 配置文件更新成功
2025-06-30 19:35:44 - core.login_manager - INFO - 凭据测试成功
2025-06-30 19:35:44 - core.login_manager - INFO - 登录管理器会话已关闭
2025-06-30 19:35:44 - core.login_manager - INFO - 登录管理器会话已关闭
2025-06-30 19:35:55 - __main__ - INFO - 开始生成并保存模拟数据，日期: 20250630
2025-06-30 19:35:55 - __main__ - INFO - 生成股票行情数据...
2025-06-30 19:35:55 - __main__ - ERROR - 保存模拟数据失败: 'name' is an invalid keyword argument for StockQuote
2025-06-30 19:36:38 - __main__ - INFO - 开始生成并保存模拟数据，日期: 20250630
2025-06-30 19:36:38 - __main__ - INFO - 生成股票行情数据...
2025-06-30 19:36:38 - __main__ - INFO - 生成板块数据...
2025-06-30 19:36:38 - __main__ - ERROR - 保存模拟数据失败: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(sqlite3.IntegrityError) NOT NULL constraint failed: plate_info.plate_code
[SQL: INSERT INTO plate_info (plate_code, plate_name, plate_type, trade_date, change_rate, stock_count, up_count, down_count, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: (None, '沪深300', None, '20250630', 4.48, 24, 43, 40, '2025-06-30 19:36:38.148571')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-30 19:37:48 - __main__ - INFO - 开始生成并保存模拟数据，日期: 20250630
2025-06-30 19:37:48 - __main__ - INFO - 生成股票行情数据...
2025-06-30 19:37:48 - __main__ - INFO - 生成板块数据...
2025-06-30 19:37:48 - __main__ - INFO - 生成龙虎榜数据...
2025-06-30 19:37:48 - __main__ - INFO - 模拟数据保存成功
2025-06-30 19:38:06 - __main__ - INFO - === 系统状态 ===
2025-06-30 19:38:06 - __main__ - INFO - 数据库URL: sqlite:///kaipanla_data.db
2025-06-30 19:38:06 - __main__ - INFO - 日志级别: DEBUG
2025-06-30 19:38:06 - __main__ - INFO - 错误统计: {'error_count': 0, 'warning_count': 0, 'errors': [], 'warnings': []}
2025-06-30 19:38:06 - utils.logger - INFO - 调用函数: get_data_statistics
2025-06-30 19:38:06 - core.data_processor - INFO - 数据统计完成: {'stock_quote_count': 14, 'stock_tag_count': 11, 'stock_concept_count': 53, 'plate_info_count': 10, 'long_hu_bang_count': 5}
2025-06-30 19:38:06 - utils.logger - INFO - 函数 get_data_statistics 执行成功
2025-06-30 19:38:06 - __main__ - INFO - 数据统计: {'stock_quote_count': 14, 'stock_tag_count': 11, 'stock_concept_count': 53, 'plate_info_count': 10, 'long_hu_bang_count': 5}
2025-06-30 19:38:50 - __main__ - INFO - 开始导出数据: 20250630
2025-06-30 19:38:50 - utils.logger - INFO - 调用函数: export_to_excel
2025-06-30 19:38:50 - core.data_processor - INFO - 开始导出数据到Excel: demo_data.xlsx
2025-06-30 19:38:51 - core.data_processor - INFO - 数据导出完成: demo_data.xlsx
2025-06-30 19:38:51 - utils.logger - INFO - 函数 export_to_excel 执行成功
2025-06-30 19:38:51 - __main__ - INFO - 数据导出成功: demo_data.xlsx
2025-06-30 19:38:51 - utils.logger - INFO - 调用函数: get_data_statistics
2025-06-30 19:38:51 - core.data_processor - INFO - 数据统计完成: {'stock_quote_count': 14, 'stock_tag_count': 11, 'stock_concept_count': 53, 'plate_info_count': 10, 'long_hu_bang_count': 5}
2025-06-30 19:38:51 - utils.logger - INFO - 函数 get_data_statistics 执行成功
2025-06-30 19:38:51 - __main__ - INFO - 数据统计: {'stock_quote_count': 14, 'stock_tag_count': 11, 'stock_concept_count': 53, 'plate_info_count': 10, 'long_hu_bang_count': 5}
2025-06-30 19:41:10 - core.login_manager - INFO - 凭据测试成功
2025-06-30 19:41:10 - core.login_manager - INFO - 登录管理器会话已关闭
2025-06-30 19:41:22 - __main__ - INFO - 开始采集今日数据: 20250630
2025-06-30 19:41:22 - utils.logger - INFO - 调用函数: get_stock_list_from_tushare
2025-06-30 19:41:22 - core.data_collector - INFO - 开始从Tushare获取股票列表
2025-06-30 19:41:22 - core.data_collector - INFO - 获取到 10 只股票
2025-06-30 19:41:22 - utils.logger - INFO - 函数 get_stock_list_from_tushare 执行成功
2025-06-30 19:41:22 - __main__ - INFO - 获取到 10 只股票
2025-06-30 19:41:22 - utils.logger - INFO - 调用函数: batch_collect_stocks
2025-06-30 19:41:22 - core.data_collector - INFO - 开始批量采集 10 只股票数据
2025-06-30 19:41:22 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:41:22 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:41:22 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 19:41:22 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:41:22 - core.http_client - INFO - 获取股票数据: 000002, 日期: 20250630
2025-06-30 19:41:22 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:41:22 - core.http_client - INFO - 获取股票数据: 000858, 日期: 20250630
2025-06-30 19:41:22 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:41:22 - core.http_client - INFO - 获取股票数据: 002415, 日期: 20250630
2025-06-30 19:41:22 - core.http_client - INFO - 获取股票数据: 600000, 日期: 20250630
2025-06-30 19:41:23 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:41:23 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000002'}
2025-06-30 19:41:23 - core.http_client - DEBUG - 股票 000002 数据获取成功
2025-06-30 19:41:23 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:41:23 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000001'}
2025-06-30 19:41:23 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 19:41:23 - core.data_collector - ERROR - 采集股票 000001 数据失败: This session is provisioning a new connection; concurrent operations are not permitted (Background on this error at: https://sqlalche.me/e/20/isce)
2025-06-30 19:41:23 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:41:23 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:41:23 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:41:23 - core.http_client - INFO - 获取股票数据: 600036, 日期: 20250630
2025-06-30 19:41:23 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000858'}
2025-06-30 19:41:23 - core.http_client - DEBUG - 股票 000858 数据获取成功
2025-06-30 19:41:23 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:41:23 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600000'}
2025-06-30 19:41:23 - core.http_client - DEBUG - 股票 600000 数据获取成功
2025-06-30 19:41:23 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:41:23 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002415'}
2025-06-30 19:41:23 - core.http_client - DEBUG - 股票 002415 数据获取成功
2025-06-30 19:41:23 - core.data_collector - ERROR - 采集股票 002415 数据失败: Method 'rollback()' can't be called here; method 'commit()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-06-30 19:41:23 - core.data_collector - ERROR - 采集股票 600000 数据失败: Method 'rollback()' can't be called here; method 'commit()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-06-30 19:41:23 - core.data_collector - DEBUG - 股票 000002 数据保存成功
2025-06-30 19:41:23 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:41:23 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:41:23 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:41:23 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:41:23 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:41:23 - core.http_client - INFO - 获取股票数据: 600519, 日期: 20250630
2025-06-30 19:41:23 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:41:23 - core.data_collector - ERROR - 解析保存股票 000858 数据失败: This transaction is closed
2025-06-30 19:41:23 - core.data_collector - ERROR - 采集股票 000858 数据失败: This transaction is closed
2025-06-30 19:41:23 - core.http_client - INFO - 获取股票数据: 000858, 日期: 20250630
2025-06-30 19:41:23 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:41:23 - core.http_client - INFO - 获取股票数据: 002594, 日期: 20250630
2025-06-30 19:41:23 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:41:23 - core.http_client - INFO - 获取股票数据: 300059, 日期: 20250630
2025-06-30 19:41:24 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:41:24 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600036'}
2025-06-30 19:41:24 - core.http_client - DEBUG - 股票 600036 数据获取成功
2025-06-30 19:41:24 - core.data_collector - ERROR - 解析保存股票 600036 数据失败: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(sqlite3.IntegrityError) UNIQUE constraint failed: stock_concept.id
[SQL: INSERT INTO stock_concept (id, symbol, concept, trade_date, created_at) VALUES (?, ?, ?, ?, ?)]
[parameters: [(69, '000002', '超级品牌', '20250630', '2025-06-30 19:41:23.851698'), (68, '000858', '白酒', '20250630', '2025-06-30 19:41:23.851691')]]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-30 19:41:24 - core.data_collector - ERROR - 采集股票 600036 数据失败: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(sqlite3.IntegrityError) UNIQUE constraint failed: stock_concept.id
[SQL: INSERT INTO stock_concept (id, symbol, concept, trade_date, created_at) VALUES (?, ?, ?, ?, ?)]
[parameters: [(69, '000002', '超级品牌', '20250630', '2025-06-30 19:41:23.851698'), (68, '000858', '白酒', '20250630', '2025-06-30 19:41:23.851691')]]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-30 19:41:24 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:41:24 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:41:24 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600519'}
2025-06-30 19:41:24 - core.http_client - DEBUG - 股票 600519 数据获取成功
2025-06-30 19:41:24 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:41:24 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000858'}
2025-06-30 19:41:24 - core.http_client - DEBUG - 股票 000858 数据获取成功
2025-06-30 19:41:24 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:41:24 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002594'}
2025-06-30 19:41:24 - core.http_client - DEBUG - 股票 002594 数据获取成功
2025-06-30 19:41:24 - core.data_collector - ERROR - 采集股票 002594 数据失败: Method 'rollback()' can't be called here; method 'commit()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-06-30 19:41:24 - core.data_collector - ERROR - 采集股票 000858 数据失败: Method 'rollback()' can't be called here; method 'commit()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-06-30 19:41:24 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:41:24 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:41:24 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:41:24 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300059'}
2025-06-30 19:41:24 - core.http_client - DEBUG - 股票 300059 数据获取成功
2025-06-30 19:41:24 - core.data_collector - ERROR - 采集股票 300059 数据失败: Method 'rollback()' can't be called here; method 'commit()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-06-30 19:41:24 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:41:24 - core.data_collector - DEBUG - 股票 600519 数据保存成功
2025-06-30 19:41:24 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:41:24 - core.data_collector - INFO - 批量采集完成: 成功 2, 失败 8
2025-06-30 19:41:24 - utils.logger - INFO - 函数 batch_collect_stocks 执行成功
2025-06-30 19:41:24 - __main__ - INFO - 股票数据采集结果: {'success': 2, 'failed': 8, 'total': 10}
2025-06-30 19:41:24 - utils.logger - INFO - 调用函数: collect_plate_data
2025-06-30 19:41:24 - core.data_collector - INFO - 开始采集板块数据，日期: 20250630
2025-06-30 19:41:24 - core.http_client - INFO - 获取板块数据，日期: 20250630
2025-06-30 19:41:26 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:41:26 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetPlateList', 'Day': '20250630', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 19:41:26 - core.http_client - ERROR - 获取板块数据时发生错误: Expecting value: line 7 column 1 (char 6)
2025-06-30 19:41:26 - utils.logger - INFO - 函数 collect_plate_data 执行成功
2025-06-30 19:41:26 - __main__ - INFO - 板块数据采集: 失败
2025-06-30 19:41:26 - utils.logger - INFO - 调用函数: collect_longhubang_data
2025-06-30 19:41:26 - core.data_collector - INFO - 开始采集龙虎榜数据，日期: 20250630
2025-06-30 19:41:26 - core.http_client - INFO - 获取龙虎榜数据，日期: 20250630
2025-06-30 19:41:27 - utils.logger - INFO - API请求成功 - URL: https://pclhb.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:41:27 - utils.logger - DEBUG - 请求参数: {'c': 'LongHuBang', 'a': 'GetLongHuBangData', 'Day': '20250630', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 19:41:27 - core.http_client - ERROR - 获取龙虎榜数据时发生错误: Expecting value: line 1 column 1 (char 0)
2025-06-30 19:41:27 - utils.logger - INFO - 函数 collect_longhubang_data 执行成功
2025-06-30 19:41:27 - __main__ - INFO - 龙虎榜数据采集: 失败
2025-06-30 19:41:27 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 19:42:07 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 19:42:08 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:42:08 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000001'}
2025-06-30 19:42:08 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 19:42:09 - core.http_client - INFO - 获取板块数据，日期: 20250630
2025-06-30 19:42:10 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:42:10 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetPlateList', 'Day': '20250630', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 19:42:10 - core.http_client - ERROR - 获取板块数据时发生错误: Expecting value: line 7 column 1 (char 6)
2025-06-30 19:42:10 - core.http_client - INFO - 获取龙虎榜数据，日期: 20250630
2025-06-30 19:42:11 - utils.logger - INFO - API请求成功 - URL: https://pclhb.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:42:11 - utils.logger - DEBUG - 请求参数: {'c': 'LongHuBang', 'a': 'GetLongHuBangData', 'Day': '20250630', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 19:42:11 - core.http_client - ERROR - 获取龙虎榜数据时发生错误: Expecting value: line 1 column 1 (char 0)
2025-06-30 19:42:11 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 19:51:44 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:51:44 - core.http_client - INFO - 获取股票数据: ['000001'], 日期: 20250630
2025-06-30 19:51:45 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:51:45 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': ['000001']}
2025-06-30 19:51:45 - core.http_client - DEBUG - 股票 ['000001'] 数据获取成功
2025-06-30 19:51:45 - core.data_collector - ERROR - 解析保存股票 ['000001'] 数据失败: (sqlite3.ProgrammingError) Error binding parameter 1: type 'list' is not supported
[SQL: SELECT stock_quote.id AS stock_quote_id, stock_quote.symbol AS stock_quote_symbol, stock_quote.trade_date AS stock_quote_trade_date, stock_quote.current_price AS stock_quote_current_price, stock_quote.open_price AS stock_quote_open_price, stock_quote.high_price AS stock_quote_high_price, stock_quote.low_price AS stock_quote_low_price, stock_quote.close_price AS stock_quote_close_price, stock_quote.volume AS stock_quote_volume, stock_quote.amount AS stock_quote_amount, stock_quote.change_rate AS stock_quote_change_rate, stock_quote.change_amount AS stock_quote_change_amount, stock_quote.created_at AS stock_quote_created_at 
FROM stock_quote 
WHERE stock_quote.symbol = ? AND stock_quote.trade_date = ?
 LIMIT ? OFFSET ?]
[parameters: (['000001'], '20250630', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-30 19:51:45 - core.data_collector - ERROR - 采集股票 ['000001'] 数据失败: (sqlite3.ProgrammingError) Error binding parameter 1: type 'list' is not supported
[SQL: SELECT stock_quote.id AS stock_quote_id, stock_quote.symbol AS stock_quote_symbol, stock_quote.trade_date AS stock_quote_trade_date, stock_quote.current_price AS stock_quote_current_price, stock_quote.open_price AS stock_quote_open_price, stock_quote.high_price AS stock_quote_high_price, stock_quote.low_price AS stock_quote_low_price, stock_quote.close_price AS stock_quote_close_price, stock_quote.volume AS stock_quote_volume, stock_quote.amount AS stock_quote_amount, stock_quote.change_rate AS stock_quote_change_rate, stock_quote.change_amount AS stock_quote_change_amount, stock_quote.created_at AS stock_quote_created_at 
FROM stock_quote 
WHERE stock_quote.symbol = ? AND stock_quote.trade_date = ?
 LIMIT ? OFFSET ?]
[parameters: (['000001'], '20250630', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-30 19:51:45 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:51:45 - utils.logger - INFO - 调用函数: collect_plate_data
2025-06-30 19:51:45 - core.data_collector - INFO - 开始采集板块数据，日期: 20250630
2025-06-30 19:51:45 - core.http_client - INFO - 获取板块数据，日期: 20250630
2025-06-30 19:51:46 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:51:46 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetPlateList', 'Day': '20250630', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 19:51:46 - core.http_client - WARNING - 板块数据API返回空内容，可能该日期无数据
2025-06-30 19:51:46 - utils.logger - INFO - 函数 collect_plate_data 执行成功
2025-06-30 19:51:46 - utils.logger - INFO - 调用函数: collect_longhubang_data
2025-06-30 19:51:46 - core.data_collector - INFO - 开始采集龙虎榜数据，日期: 20250630
2025-06-30 19:51:46 - core.http_client - INFO - 获取龙虎榜数据，日期: 20250630
2025-06-30 19:51:48 - utils.logger - INFO - API请求成功 - URL: https://pclhb.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:51:48 - utils.logger - DEBUG - 请求参数: {'c': 'LongHuBang', 'a': 'GetLongHuBangData', 'Day': '20250630', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 19:51:48 - core.http_client - WARNING - 龙虎榜数据API返回空内容，可能该日期无数据
2025-06-30 19:51:48 - utils.logger - INFO - 函数 collect_longhubang_data 执行成功
2025-06-30 19:51:48 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 19:53:47 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:53:47 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 19:53:49 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:53:49 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000001'}
2025-06-30 19:53:49 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 19:53:49 - core.data_collector - DEBUG - 股票 000001 数据保存成功
2025-06-30 19:53:49 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:53:49 - utils.logger - INFO - 调用函数: batch_collect_stocks
2025-06-30 19:53:49 - core.data_collector - INFO - 开始批量采集 2 只股票数据
2025-06-30 19:53:49 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:53:49 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 19:53:49 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 19:53:49 - core.http_client - INFO - 获取股票数据: 000002, 日期: 20250630
2025-06-30 19:53:50 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:53:50 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000001'}
2025-06-30 19:53:50 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 19:53:50 - core.data_collector - DEBUG - 股票 000001 数据保存成功
2025-06-30 19:53:50 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:53:50 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 19:53:50 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000002'}
2025-06-30 19:53:50 - core.http_client - DEBUG - 股票 000002 数据获取成功
2025-06-30 19:53:50 - core.data_collector - DEBUG - 股票 000002 数据保存成功
2025-06-30 19:53:50 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 19:53:50 - core.data_collector - INFO - 批量采集完成: 成功 2, 失败 0
2025-06-30 19:53:50 - utils.logger - INFO - 函数 batch_collect_stocks 执行成功
2025-06-30 19:53:50 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 20:08:27 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 20:08:29 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:08:29 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000001'}
2025-06-30 20:08:29 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 20:08:29 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 20:09:48 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 20:09:49 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:09:49 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000001'}
2025-06-30 20:09:49 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 20:09:49 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 20:12:44 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 20:12:44 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 20:12:45 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:12:45 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000001'}
2025-06-30 20:12:45 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 20:12:45 - core.data_collector - DEBUG - 股票 000001 数据保存成功
2025-06-30 20:12:45 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 20:12:45 - utils.logger - INFO - 调用函数: collect_plate_data
2025-06-30 20:12:45 - core.data_collector - INFO - 板块数据已通过股票API获取，日期: 20250630
2025-06-30 20:12:45 - core.data_collector - INFO - 板块数据包含在每个股票的stockplate和careplate字段中
2025-06-30 20:12:45 - core.data_collector - WARNING - 独立的板块API已失效，板块数据通过股票数据收集获得
2025-06-30 20:12:45 - utils.logger - INFO - 函数 collect_plate_data 执行成功
2025-06-30 20:12:45 - utils.logger - INFO - 调用函数: collect_longhubang_data
2025-06-30 20:12:45 - core.data_collector - INFO - 龙虎榜数据API当前不可用，日期: 20250630
2025-06-30 20:12:45 - core.data_collector - WARNING - 独立的龙虎榜API返回空内容，可能已失效或需要特殊权限
2025-06-30 20:12:45 - core.data_collector - INFO - 建议通过其他数据源获取龙虎榜数据
2025-06-30 20:12:45 - utils.logger - INFO - 函数 collect_longhubang_data 执行成功
2025-06-30 20:12:45 - utils.logger - INFO - 调用函数: batch_collect_stocks
2025-06-30 20:12:45 - core.data_collector - INFO - 开始批量采集 2 只股票数据
2025-06-30 20:12:45 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 20:12:45 - core.http_client - INFO - 获取股票数据: 000002, 日期: 20250630
2025-06-30 20:12:45 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 20:12:45 - core.http_client - INFO - 获取股票数据: 000858, 日期: 20250630
2025-06-30 20:12:46 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:12:46 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000002'}
2025-06-30 20:12:46 - core.http_client - DEBUG - 股票 000002 数据获取成功
2025-06-30 20:12:46 - core.data_collector - DEBUG - 股票 000002 数据保存成功
2025-06-30 20:12:46 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 20:12:46 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:12:46 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000858'}
2025-06-30 20:12:46 - core.http_client - DEBUG - 股票 000858 数据获取成功
2025-06-30 20:12:46 - core.data_collector - DEBUG - 股票 000858 数据保存成功
2025-06-30 20:12:46 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 20:12:46 - core.data_collector - INFO - 批量采集完成: 成功 2, 失败 0
2025-06-30 20:12:46 - utils.logger - INFO - 函数 batch_collect_stocks 执行成功
2025-06-30 20:12:46 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 20:22:34 - core.http_client - INFO - 获取龙虎榜数据，日期: 20250630
2025-06-30 20:22:36 - utils.logger - INFO - API请求成功 - URL: https://pclhb.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:22:36 - utils.logger - DEBUG - 请求参数: {'c': 'LongHuBang', 'a': 'GetStockList', 'Index': '0', 'st': '300', 'Time': '', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 20:22:36 - core.http_client - DEBUG - 龙虎榜数据获取成功
2025-06-30 20:22:36 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 20:32:53 - core.http_client - INFO - 获取板块数据，日期: 2025-06-30
2025-06-30 20:32:54 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:32:54 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'Date': '2025-06-30', 'PType': '2', 'POrder': '1', 'ZSType': '5', 'rate': '1', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '20', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 20:32:54 - core.http_client - DEBUG - 板块数据获取成功
2025-06-30 20:32:54 - core.http_client - INFO - 获取板块数据，日期: 20250630
2025-06-30 20:32:55 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:32:55 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'Date': '20250630', 'PType': '2', 'POrder': '1', 'ZSType': '5', 'rate': '1', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '20', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 20:32:55 - core.http_client - DEBUG - 板块数据获取成功
2025-06-30 20:32:55 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 20:34:17 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 20:34:18 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:34:18 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000001'}
2025-06-30 20:34:18 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 20:34:18 - core.http_client - INFO - 获取板块数据，日期: 2025-06-30
2025-06-30 20:34:19 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:34:19 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'Date': '2025-06-30', 'PType': '2', 'POrder': '1', 'ZSType': '5', 'rate': '1', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '20', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 20:34:19 - core.http_client - DEBUG - 板块数据获取成功
2025-06-30 20:34:19 - core.http_client - INFO - 获取核心板块数据，ZSType: 7
2025-06-30 20:34:20 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:34:20 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '17', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 20:34:20 - core.http_client - DEBUG - 核心板块数据获取成功
2025-06-30 20:34:20 - core.http_client - INFO - 获取龙虎榜数据，日期: 20250630
2025-06-30 20:34:22 - utils.logger - INFO - API请求成功 - URL: https://pclhb.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:34:22 - utils.logger - DEBUG - 请求参数: {'c': 'LongHuBang', 'a': 'GetStockList', 'Index': '0', 'st': '300', 'Time': '', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 20:34:22 - core.http_client - DEBUG - 龙虎榜数据获取成功
2025-06-30 20:34:22 - core.http_client - INFO - 获取新闻数据，索引: 0, 数量: 8
2025-06-30 20:34:23 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:34:23 - utils.logger - DEBUG - 请求参数: {'c': 'PCNewsFlash', 'a': 'GetList', 'Index': '0', 'st': '8', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 20:34:23 - core.http_client - WARNING - 新闻数据API返回空内容
2025-06-30 20:34:23 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 20:35:49 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 20:35:49 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 20:35:51 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:35:51 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000001'}
2025-06-30 20:35:51 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 20:35:51 - core.data_collector - DEBUG - 股票 000001 数据保存成功
2025-06-30 20:35:51 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 20:35:51 - utils.logger - INFO - 调用函数: collect_plate_data
2025-06-30 20:35:51 - core.data_collector - INFO - 开始采集板块数据，日期: 20250630
2025-06-30 20:35:51 - core.http_client - INFO - 获取板块数据，日期: 2025-06-30
2025-06-30 20:35:52 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:35:52 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'Date': '2025-06-30', 'PType': '2', 'POrder': '1', 'ZSType': '5', 'rate': '1', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '20', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 20:35:52 - core.http_client - DEBUG - 板块数据获取成功
2025-06-30 20:35:52 - core.data_collector - ERROR - 解析保存板块数据失败: 'str' object has no attribute 'get'
2025-06-30 20:35:52 - core.data_collector - ERROR - 采集板块数据失败: 'str' object has no attribute 'get'
2025-06-30 20:35:52 - utils.logger - INFO - 函数 collect_plate_data 执行成功
2025-06-30 20:35:52 - utils.logger - INFO - 调用函数: collect_longhubang_data
2025-06-30 20:35:52 - core.data_collector - INFO - 开始采集龙虎榜数据，日期: 20250630
2025-06-30 20:35:52 - core.http_client - INFO - 获取龙虎榜数据，日期: 20250630
2025-06-30 20:35:53 - utils.logger - INFO - API请求成功 - URL: https://pclhb.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:35:53 - utils.logger - DEBUG - 请求参数: {'c': 'LongHuBang', 'a': 'GetStockList', 'Index': '0', 'st': '300', 'Time': '', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 20:35:53 - core.http_client - DEBUG - 龙虎榜数据获取成功
2025-06-30 20:35:53 - core.data_collector - INFO - 龙虎榜数据保存成功
2025-06-30 20:35:53 - utils.logger - INFO - 函数 collect_longhubang_data 执行成功
2025-06-30 20:35:53 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 20:36:16 - core.http_client - INFO - 获取板块数据，日期: 2025-06-30
2025-06-30 20:36:17 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:36:17 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'Date': '2025-06-30', 'PType': '2', 'POrder': '1', 'ZSType': '5', 'rate': '1', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '20', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 20:36:17 - core.http_client - DEBUG - 板块数据获取成功
2025-06-30 20:36:17 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 20:38:19 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 20:38:19 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 20:38:20 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:38:20 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000001'}
2025-06-30 20:38:20 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 20:38:20 - core.data_collector - DEBUG - 股票 000001 数据保存成功
2025-06-30 20:38:20 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 20:38:20 - utils.logger - INFO - 调用函数: collect_plate_data
2025-06-30 20:38:20 - core.data_collector - INFO - 开始采集板块数据，日期: 20250630
2025-06-30 20:38:20 - core.http_client - INFO - 获取板块数据，日期: 2025-06-30
2025-06-30 20:38:21 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:38:21 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'Date': '2025-06-30', 'PType': '2', 'POrder': '1', 'ZSType': '5', 'rate': '1', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '20', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 20:38:21 - core.http_client - DEBUG - 板块数据获取成功
2025-06-30 20:38:21 - core.data_collector - INFO - 解析了 20 个板块数据
2025-06-30 20:38:21 - core.data_collector - INFO - 板块数据保存成功
2025-06-30 20:38:21 - utils.logger - INFO - 函数 collect_plate_data 执行成功
2025-06-30 20:38:21 - utils.logger - INFO - 调用函数: collect_longhubang_data
2025-06-30 20:38:21 - core.data_collector - INFO - 开始采集龙虎榜数据，日期: 20250630
2025-06-30 20:38:21 - core.http_client - INFO - 获取龙虎榜数据，日期: 20250630
2025-06-30 20:38:22 - utils.logger - INFO - API请求成功 - URL: https://pclhb.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 20:38:22 - utils.logger - DEBUG - 请求参数: {'c': 'LongHuBang', 'a': 'GetStockList', 'Index': '0', 'st': '300', 'Time': '', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 20:38:22 - core.http_client - DEBUG - 龙虎榜数据获取成功
2025-06-30 20:38:22 - core.data_collector - INFO - 龙虎榜数据保存成功
2025-06-30 20:38:22 - utils.logger - INFO - 函数 collect_longhubang_data 执行成功
2025-06-30 20:38:22 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 21:50:16 - core.http_client - INFO - 获取核心板块数据，ZSType: 7
2025-06-30 21:50:17 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 21:50:17 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '17', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 21:50:17 - core.http_client - DEBUG - 核心板块数据获取成功
2025-06-30 21:50:17 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 21:51:08 - core.http_client - INFO - 获取核心板块数据，ZSType: 7
2025-06-30 21:51:09 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 21:51:09 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '17', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 21:51:09 - core.http_client - DEBUG - 核心板块数据获取成功
2025-06-30 21:51:09 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 21:59:34 - utils.logger - INFO - 调用函数: collect_plate_strength_data
2025-06-30 21:59:34 - core.data_collector - INFO - 开始采集板块强度数据，日期: 20250630
2025-06-30 21:59:34 - core.http_client - INFO - 获取板块强度数据，索引: 0, 数量: 17
2025-06-30 21:59:35 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 21:59:35 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '17', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 21:59:35 - core.http_client - DEBUG - 板块强度数据获取成功
2025-06-30 21:59:35 - core.data_collector - ERROR - 采集板块强度数据失败: name 'create_engine' is not defined
2025-06-30 21:59:35 - utils.logger - INFO - 函数 collect_plate_strength_data 执行成功
2025-06-30 21:59:35 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 22:00:07 - utils.logger - INFO - 调用函数: collect_plate_strength_data
2025-06-30 22:00:07 - core.data_collector - INFO - 开始采集板块强度数据，日期: 20250630
2025-06-30 22:00:07 - core.http_client - INFO - 获取板块强度数据，索引: 0, 数量: 17
2025-06-30 22:00:08 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:00:08 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '17', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 22:00:08 - core.http_client - DEBUG - 板块强度数据获取成功
2025-06-30 22:00:08 - core.data_collector - ERROR - 采集板块强度数据失败: name 'Config' is not defined
2025-06-30 22:00:08 - utils.logger - INFO - 函数 collect_plate_strength_data 执行成功
2025-06-30 22:00:08 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 22:00:31 - utils.logger - INFO - 调用函数: collect_plate_strength_data
2025-06-30 22:00:31 - core.data_collector - INFO - 开始采集板块强度数据，日期: 20250630
2025-06-30 22:00:31 - core.http_client - INFO - 获取板块强度数据，索引: 0, 数量: 17
2025-06-30 22:00:32 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:00:32 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '17', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 22:00:32 - core.http_client - DEBUG - 板块强度数据获取成功
2025-06-30 22:00:32 - core.data_collector - ERROR - 采集板块强度数据失败: name 'Config' is not defined
2025-06-30 22:00:32 - utils.logger - INFO - 函数 collect_plate_strength_data 执行成功
2025-06-30 22:00:32 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 22:01:05 - utils.logger - INFO - 调用函数: collect_plate_strength_data
2025-06-30 22:01:05 - core.data_collector - INFO - 开始采集板块强度数据，日期: 20250630
2025-06-30 22:01:05 - core.http_client - INFO - 获取板块强度数据，索引: 0, 数量: 17
2025-06-30 22:01:06 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:01:06 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '17', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 22:01:06 - core.http_client - DEBUG - 板块强度数据获取成功
2025-06-30 22:01:06 - core.data_collector - INFO - 板块强度数据保存完成，新增 17 条记录
2025-06-30 22:01:06 - core.data_collector - INFO - 板块强度数据采集完成，日期: 20250630
2025-06-30 22:01:06 - utils.logger - INFO - 函数 collect_plate_strength_data 执行成功
2025-06-30 22:01:06 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 22:04:09 - core.http_client - INFO - 获取板块强度数据，索引: 0, 数量: 17
2025-06-30 22:04:10 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:04:10 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '17', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 22:04:10 - core.http_client - DEBUG - 板块强度数据获取成功
2025-06-30 22:04:10 - core.http_client - INFO - 获取板块强度数据，索引: 0, 数量: 50
2025-06-30 22:04:11 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:04:11 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '50', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 22:04:11 - core.http_client - DEBUG - 板块强度数据获取成功
2025-06-30 22:04:11 - core.http_client - INFO - 获取板块强度数据，索引: 0, 数量: 100
2025-06-30 22:04:12 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:04:12 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '100', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 22:04:12 - core.http_client - DEBUG - 板块强度数据获取成功
2025-06-30 22:04:12 - core.http_client - INFO - 获取板块强度数据，索引: 0, 数量: 200
2025-06-30 22:04:13 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:04:13 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '200', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 22:04:13 - core.http_client - DEBUG - 板块强度数据获取成功
2025-06-30 22:04:13 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 22:06:22 - utils.logger - INFO - 调用函数: collect_plate_strength_data
2025-06-30 22:06:22 - core.data_collector - INFO - 开始采集板块强度数据，日期: 20250630
2025-06-30 22:06:22 - core.http_client - INFO - 获取板块强度数据，索引: 0, 数量: 300
2025-06-30 22:06:24 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:06:24 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '300', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 22:06:24 - core.http_client - DEBUG - 板块强度数据获取成功
2025-06-30 22:06:24 - core.data_collector - INFO - 板块强度数据保存完成，新增 255 条记录
2025-06-30 22:06:24 - core.data_collector - INFO - 板块强度数据采集完成，日期: 20250630
2025-06-30 22:06:24 - utils.logger - INFO - 函数 collect_plate_strength_data 执行成功
2025-06-30 22:06:24 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 22:10:40 - core.http_client - INFO - 获取核心板块数据，ZSType: 7
2025-06-30 22:10:42 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:10:42 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '300', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 22:10:42 - core.http_client - DEBUG - 核心板块数据获取成功
2025-06-30 22:10:42 - core.http_client - INFO - HTTP会话已关闭
2025-06-30 22:11:59 - utils.logger - INFO - 调用函数: collect_plate_data
2025-06-30 22:11:59 - core.data_collector - INFO - 开始采集板块数据，日期: 20250630
2025-06-30 22:11:59 - core.http_client - INFO - 获取板块数据，日期: 2025-06-30
2025-06-30 22:12:00 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:00 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'Date': '2025-06-30', 'PType': '2', 'POrder': '1', 'ZSType': '5', 'rate': '1', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '20', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 22:12:00 - core.http_client - DEBUG - 板块数据获取成功
2025-06-30 22:12:00 - core.data_collector - INFO - 解析了 20 个板块数据
2025-06-30 22:12:00 - core.data_collector - INFO - 板块数据保存成功
2025-06-30 22:12:00 - utils.logger - INFO - 函数 collect_plate_data 执行成功
2025-06-30 22:12:00 - utils.logger - INFO - 调用函数: collect_plate_strength_data
2025-06-30 22:12:00 - core.data_collector - INFO - 开始采集板块强度数据，日期: 20250630
2025-06-30 22:12:00 - core.http_client - INFO - 获取板块强度数据，索引: 0, 数量: 300
2025-06-30 22:12:01 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:01 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetZSIndexPlate', 'SelType': '2', 'ZSType': '7', 'PType': '1', 'POrder': '0', 'PStart': '', 'PEnd': '', 'PIndex': '0', 'Pst': '300', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 22:12:01 - core.http_client - DEBUG - 板块强度数据获取成功
2025-06-30 22:12:02 - core.data_collector - INFO - 板块强度数据保存完成，新增 255 条记录
2025-06-30 22:12:02 - core.data_collector - INFO - 板块强度数据采集完成，日期: 20250630
2025-06-30 22:12:02 - utils.logger - INFO - 函数 collect_plate_strength_data 执行成功
2025-06-30 22:12:02 - utils.logger - INFO - 调用函数: collect_longhubang_data
2025-06-30 22:12:02 - core.data_collector - INFO - 开始采集龙虎榜数据，日期: 20250630
2025-06-30 22:12:02 - core.http_client - INFO - 获取龙虎榜数据，日期: 20250630
2025-06-30 22:12:03 - utils.logger - INFO - API请求成功 - URL: https://pclhb.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:03 - utils.logger - DEBUG - 请求参数: {'c': 'LongHuBang', 'a': 'GetStockList', 'Index': '0', 'st': '300', 'Time': '', 'UserID': 710743, 'Token': '********************************'}
2025-06-30 22:12:03 - core.http_client - DEBUG - 龙虎榜数据获取成功
2025-06-30 22:12:03 - core.data_collector - INFO - 龙虎榜数据保存成功
2025-06-30 22:12:03 - utils.logger - INFO - 函数 collect_longhubang_data 执行成功
2025-06-30 22:12:03 - utils.logger - INFO - 调用函数: batch_collect_stocks
2025-06-30 22:12:03 - core.data_collector - INFO - 开始批量采集 15 只股票数据
2025-06-30 22:12:03 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:03 - core.http_client - INFO - 获取股票数据: 600887, 日期: 20250630
2025-06-30 22:12:03 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:03 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:03 - core.http_client - INFO - 获取股票数据: 300015, 日期: 20250630
2025-06-30 22:12:03 - core.http_client - INFO - 获取股票数据: 002304, 日期: 20250630
2025-06-30 22:12:04 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:04 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600887'}
2025-06-30 22:12:04 - core.http_client - DEBUG - 股票 600887 数据获取成功
2025-06-30 22:12:04 - core.data_collector - DEBUG - 股票 600887 数据保存成功
2025-06-30 22:12:04 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:04 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:04 - core.http_client - INFO - 获取股票数据: 000725, 日期: 20250630
2025-06-30 22:12:04 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:04 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300015'}
2025-06-30 22:12:04 - core.http_client - DEBUG - 股票 300015 数据获取成功
2025-06-30 22:12:04 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:04 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002304'}
2025-06-30 22:12:04 - core.http_client - DEBUG - 股票 002304 数据获取成功
2025-06-30 22:12:04 - core.data_collector - DEBUG - 股票 300015 数据保存成功
2025-06-30 22:12:04 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:04 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:04 - core.http_client - INFO - 获取股票数据: 600276, 日期: 20250630
2025-06-30 22:12:04 - core.data_collector - DEBUG - 股票 002304 数据保存成功
2025-06-30 22:12:04 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:04 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:04 - core.http_client - INFO - 获取股票数据: 000001, 日期: 20250630
2025-06-30 22:12:05 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:05 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000725'}
2025-06-30 22:12:05 - core.http_client - DEBUG - 股票 000725 数据获取成功
2025-06-30 22:12:05 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:05 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600276'}
2025-06-30 22:12:05 - core.http_client - DEBUG - 股票 600276 数据获取成功
2025-06-30 22:12:05 - core.data_collector - DEBUG - 股票 000725 数据保存成功
2025-06-30 22:12:05 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:05 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:05 - core.http_client - INFO - 获取股票数据: 000002, 日期: 20250630
2025-06-30 22:12:05 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:05 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000001'}
2025-06-30 22:12:05 - core.http_client - DEBUG - 股票 000001 数据获取成功
2025-06-30 22:12:06 - core.data_collector - DEBUG - 股票 600276 数据保存成功
2025-06-30 22:12:06 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:06 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:06 - core.http_client - INFO - 获取股票数据: 600000, 日期: 20250630
2025-06-30 22:12:06 - core.data_collector - DEBUG - 股票 000001 数据保存成功
2025-06-30 22:12:06 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:06 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:06 - core.http_client - INFO - 获取股票数据: 600036, 日期: 20250630
2025-06-30 22:12:07 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:07 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000002'}
2025-06-30 22:12:07 - core.http_client - DEBUG - 股票 000002 数据获取成功
2025-06-30 22:12:07 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:07 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600000'}
2025-06-30 22:12:07 - core.http_client - DEBUG - 股票 600000 数据获取成功
2025-06-30 22:12:07 - core.data_collector - DEBUG - 股票 000002 数据保存成功
2025-06-30 22:12:07 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:07 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:07 - core.http_client - INFO - 获取股票数据: 000858, 日期: 20250630
2025-06-30 22:12:07 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:07 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600036'}
2025-06-30 22:12:07 - core.http_client - DEBUG - 股票 600036 数据获取成功
2025-06-30 22:12:07 - core.data_collector - DEBUG - 股票 600000 数据保存成功
2025-06-30 22:12:07 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:07 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:07 - core.http_client - INFO - 获取股票数据: 002415, 日期: 20250630
2025-06-30 22:12:07 - core.data_collector - DEBUG - 股票 600036 数据保存成功
2025-06-30 22:12:07 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:07 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:07 - core.http_client - INFO - 获取股票数据: 002594, 日期: 20250630
2025-06-30 22:12:08 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:08 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000858'}
2025-06-30 22:12:08 - core.http_client - DEBUG - 股票 000858 数据获取成功
2025-06-30 22:12:08 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:08 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002415'}
2025-06-30 22:12:08 - core.http_client - DEBUG - 股票 002415 数据获取成功
2025-06-30 22:12:08 - core.data_collector - DEBUG - 股票 000858 数据保存成功
2025-06-30 22:12:08 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:08 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:08 - core.http_client - INFO - 获取股票数据: 300059, 日期: 20250630
2025-06-30 22:12:08 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:08 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '002594'}
2025-06-30 22:12:08 - core.http_client - DEBUG - 股票 002594 数据获取成功
2025-06-30 22:12:08 - core.data_collector - DEBUG - 股票 002415 数据保存成功
2025-06-30 22:12:08 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:08 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:08 - core.http_client - INFO - 获取股票数据: 600519, 日期: 20250630
2025-06-30 22:12:08 - core.data_collector - DEBUG - 股票 002594 数据保存成功
2025-06-30 22:12:08 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:08 - utils.logger - INFO - 调用函数: collect_stock_data
2025-06-30 22:12:08 - core.http_client - INFO - 获取股票数据: 000002, 日期: 20250630
2025-06-30 22:12:09 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:09 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '300059'}
2025-06-30 22:12:09 - core.http_client - DEBUG - 股票 300059 数据获取成功
2025-06-30 22:12:09 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:09 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '600519'}
2025-06-30 22:12:09 - core.http_client - DEBUG - 股票 600519 数据获取成功
2025-06-30 22:12:09 - core.data_collector - DEBUG - 股票 300059 数据保存成功
2025-06-30 22:12:09 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:09 - core.data_collector - DEBUG - 股票 600519 数据保存成功
2025-06-30 22:12:09 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:09 - utils.logger - INFO - API请求成功 - URL: https://pchq.kaipanla.com/w1/api/index.php, 状态码: 200
2025-06-30 22:12:09 - utils.logger - DEBUG - 请求参数: {'c': 'PCArrangeData', 'a': 'GetHQPlate', 'Day': '20250630', 'SelType': '1,2,3,8,9,5,6,7', 'UserID': 710743, 'Token': '********************************', 'StockID': '000002'}
2025-06-30 22:12:09 - core.http_client - DEBUG - 股票 000002 数据获取成功
2025-06-30 22:12:09 - core.data_collector - DEBUG - 股票 000002 数据保存成功
2025-06-30 22:12:09 - utils.logger - INFO - 函数 collect_stock_data 执行成功
2025-06-30 22:12:09 - core.data_collector - INFO - 批量采集完成: 成功 15, 失败 0
2025-06-30 22:12:09 - utils.logger - INFO - 函数 batch_collect_stocks 执行成功
2025-06-30 22:12:09 - core.http_client - INFO - HTTP会话已关闭
