"""
数据采集核心模块
实现板块列表、个股列表等核心数据的采集功能
"""
import json
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import tushare as ts

from core.http_client import KaipanlaClient
from models.database import get_session, StockInfo, StockQuote, StockTag, StockConcept, PlateInfo, LongHuBang
from utils.logger import setup_logger, log_function_call
from config import get_config

config = get_config()
logger = setup_logger(__name__)

class DataCollector:
    """数据采集器"""
    
    def __init__(self):
        self.client = KaipanlaClient()
        self.session = get_session()
        
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
    
    def close(self):
        """关闭资源"""
        if hasattr(self, 'client'):
            self.client.close()
        if hasattr(self, 'session'):
            self.session.close()
    
    @log_function_call
    def get_stock_list_from_tushare(self) -> List[str]:
        """
        从Tushare获取股票列表
        
        Returns:
            List[str]: 股票代码列表
        """
        try:
            logger.info("开始从Tushare获取股票列表")
            
            # 注意：这里需要替换为您的Tushare token
            # pro = ts.pro_api('your_tushare_token_here')
            
            # 临时使用固定的股票列表进行测试
            test_stocks = [
                '000001', '000002', '000858', '002415', '600000', 
                '600036', '600519', '000858', '002594', '300059'
            ]
            
            logger.info(f"获取到 {len(test_stocks)} 只股票")
            return test_stocks
            
        except Exception as e:
            logger.error(f"从Tushare获取股票列表失败: {str(e)}")
            return []
    
    @log_function_call
    def collect_stock_data(self, stock_code: str, trade_date: str) -> bool:
        """
        采集单只股票数据
        
        Args:
            stock_code: 股票代码
            trade_date: 交易日期
            
        Returns:
            bool: 采集是否成功
        """
        try:
            # 获取股票数据
            data = self.client.get_stock_data(stock_code, trade_date)
            if not data:
                return False
            
            # 解析并保存数据
            self._parse_and_save_stock_data(stock_code, trade_date, data)
            return True
            
        except Exception as e:
            logger.error(f"采集股票 {stock_code} 数据失败: {str(e)}")
            return False
    
    def _parse_and_save_stock_data(self, stock_code: str, trade_date: str, data: Dict[str, Any]):
        """
        解析并保存股票数据
        
        Args:
            stock_code: 股票代码
            trade_date: 交易日期
            data: API返回的数据
        """
        try:
            # 解析基本行情数据
            if 'trend' in data:
                trend_data = data['trend']
                
                # 保存行情数据
                quote = StockQuote(
                    symbol=stock_code,
                    trade_date=trade_date,
                    current_price=trend_data.get('price'),
                    open_price=trend_data.get('open'),
                    high_price=trend_data.get('high'),
                    low_price=trend_data.get('low'),
                    close_price=trend_data.get('close'),
                    volume=trend_data.get('volume'),
                    amount=trend_data.get('amount'),
                    change_rate=trend_data.get('change_rate'),
                    change_amount=trend_data.get('change_amount')
                )
                
                # 检查是否已存在
                existing = self.session.query(StockQuote).filter_by(
                    symbol=stock_code, trade_date=trade_date
                ).first()
                
                if not existing:
                    self.session.add(quote)
            
            # 解析标签数据
            if 'pankou' in data and 'tag' in data['pankou']:
                tag_content = data['pankou']['tag']
                if tag_content:
                    # 检查是否已存在
                    existing_tag = self.session.query(StockTag).filter_by(
                        symbol=stock_code, trade_date=trade_date, tag=tag_content
                    ).first()
                    
                    if not existing_tag:
                        tag = StockTag(
                            symbol=stock_code,
                            tag=tag_content,
                            trade_date=trade_date
                        )
                        self.session.add(tag)
            
            # 解析概念数据
            if 'stockplate' in data:
                concepts = data['stockplate']
                for concept_info in concepts:
                    if isinstance(concept_info, list) and len(concept_info) > 0:
                        concept_name = concept_info[0]
                        
                        # 检查是否已存在
                        existing_concept = self.session.query(StockConcept).filter_by(
                            symbol=stock_code, trade_date=trade_date, concept=concept_name
                        ).first()
                        
                        if not existing_concept:
                            concept = StockConcept(
                                symbol=stock_code,
                                concept=concept_name,
                                trade_date=trade_date
                            )
                            self.session.add(concept)
            
            # 提交事务
            self.session.commit()
            logger.debug(f"股票 {stock_code} 数据保存成功")
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"解析保存股票 {stock_code} 数据失败: {str(e)}")
            raise
    
    @log_function_call
    def collect_plate_data(self, trade_date: str) -> bool:
        """
        采集板块数据
        
        Args:
            trade_date: 交易日期
            
        Returns:
            bool: 采集是否成功
        """
        try:
            logger.info(f"开始采集板块数据，日期: {trade_date}")
            
            # 获取板块数据
            data = self.client.get_plate_data(trade_date)
            if not data:
                return False
            
            # 解析并保存板块数据
            self._parse_and_save_plate_data(trade_date, data)
            return True
            
        except Exception as e:
            logger.error(f"采集板块数据失败: {str(e)}")
            return False
    
    def _parse_and_save_plate_data(self, trade_date: str, data: Dict[str, Any]):
        """
        解析并保存板块数据
        
        Args:
            trade_date: 交易日期
            data: API返回的数据
        """
        try:
            # 这里需要根据实际API返回的数据结构进行解析
            # 由于没有实际的板块数据结构，这里提供一个示例框架
            
            if 'plates' in data:
                plates = data['plates']
                for plate_info in plates:
                    plate = PlateInfo(
                        plate_code=plate_info.get('code'),
                        plate_name=plate_info.get('name'),
                        plate_type=plate_info.get('type'),
                        trade_date=trade_date,
                        change_rate=plate_info.get('change_rate'),
                        stock_count=plate_info.get('stock_count'),
                        up_count=plate_info.get('up_count'),
                        down_count=plate_info.get('down_count')
                    )
                    
                    # 检查是否已存在
                    existing = self.session.query(PlateInfo).filter_by(
                        plate_code=plate_info.get('code'), trade_date=trade_date
                    ).first()
                    
                    if not existing:
                        self.session.add(plate)
            
            self.session.commit()
            logger.info("板块数据保存成功")
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"解析保存板块数据失败: {str(e)}")
            raise
    
    @log_function_call
    def collect_longhubang_data(self, trade_date: str) -> bool:
        """
        采集龙虎榜数据
        
        Args:
            trade_date: 交易日期
            
        Returns:
            bool: 采集是否成功
        """
        try:
            logger.info(f"开始采集龙虎榜数据，日期: {trade_date}")
            
            # 获取龙虎榜数据
            data = self.client.get_longhubang_data(trade_date)
            if not data:
                return False
            
            # 解析并保存龙虎榜数据
            self._parse_and_save_longhubang_data(trade_date, data)
            return True
            
        except Exception as e:
            logger.error(f"采集龙虎榜数据失败: {str(e)}")
            return False
    
    def _parse_and_save_longhubang_data(self, trade_date: str, data: Dict[str, Any]):
        """
        解析并保存龙虎榜数据
        """
        try:
            # 根据实际API返回结构解析数据
            if 'longhubang' in data:
                lhb_data = data['longhubang']
                for item in lhb_data:
                    lhb = LongHuBang(
                        symbol=item.get('symbol'),
                        trade_date=trade_date,
                        reason=item.get('reason'),
                        buy_amount=item.get('buy_amount'),
                        sell_amount=item.get('sell_amount'),
                        net_amount=item.get('net_amount'),
                        department=item.get('department')
                    )
                    
                    # 检查是否已存在
                    existing = self.session.query(LongHuBang).filter_by(
                        symbol=item.get('symbol'), trade_date=trade_date,
                        department=item.get('department')
                    ).first()
                    
                    if not existing:
                        self.session.add(lhb)
            
            self.session.commit()
            logger.info("龙虎榜数据保存成功")
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"解析保存龙虎榜数据失败: {str(e)}")
            raise
    
    @log_function_call
    def batch_collect_stocks(self, stock_list: List[str], trade_date: str, max_workers: int = 5) -> Dict[str, int]:
        """
        批量采集股票数据
        
        Args:
            stock_list: 股票代码列表
            trade_date: 交易日期
            max_workers: 最大并发数
            
        Returns:
            Dict: 采集结果统计
        """
        results = {'success': 0, 'failed': 0, 'total': len(stock_list)}
        
        logger.info(f"开始批量采集 {len(stock_list)} 只股票数据")
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交任务
            future_to_stock = {
                executor.submit(self.collect_stock_data, stock, trade_date): stock 
                for stock in stock_list
            }
            
            # 处理结果
            for future in as_completed(future_to_stock):
                stock = future_to_stock[future]
                try:
                    success = future.result()
                    if success:
                        results['success'] += 1
                    else:
                        results['failed'] += 1
                except Exception as e:
                    logger.error(f"股票 {stock} 采集异常: {str(e)}")
                    results['failed'] += 1
        
        logger.info(f"批量采集完成: 成功 {results['success']}, 失败 {results['failed']}")
        return results
