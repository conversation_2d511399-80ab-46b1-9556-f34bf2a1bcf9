"""
数据采集核心模块
实现板块列表、个股列表等核心数据的采集功能
"""
import json
import os
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import tushare as ts

from core.http_client import KaipanlaClient
from models.database import get_session, StockInfo, StockQuote, StockTag, StockConcept, PlateInfo, LongHuBang
from utils.logger import setup_logger, log_function_call
from config import get_config

config = get_config()
logger = setup_logger(__name__)

class DataCollector:
    """数据采集器"""
    
    def __init__(self):
        self.client = KaipanlaClient()
        self.session = get_session()
        
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
    
    def close(self):
        """关闭资源"""
        if hasattr(self, 'client'):
            self.client.close()
        if hasattr(self, 'session'):
            self.session.close()
    
    @log_function_call
    def get_stock_list_from_tushare(self) -> List[str]:
        """
        获取完整股票列表（使用开盘啦股票排行榜API）

        Returns:
            List[str]: 股票代码列表
        """
        try:
            logger.info("开始获取完整股票列表")

            # 首先尝试从开盘啦股票排行榜API获取完整股票列表
            try:
                data = self.client.get_stock_ranking_list(start_index=0, count=10000)  # 使用更大的数量确保获取全部股票
                if data and isinstance(data, dict) and 'list' in data:
                    stock_list = data['list']
                    stock_codes = []

                    for item in stock_list:
                        if isinstance(item, dict) and 'Code' in item:
                            code = str(item['Code'])
                            if code.isdigit() and len(code) == 6:
                                stock_codes.append(code)

                    if len(stock_codes) > 1000:  # 确保获取到足够多的股票
                        logger.info(f"从开盘啦API获取到 {len(stock_codes)} 只股票")
                        return stock_codes
                    else:
                        logger.warning(f"从开盘啦API获取的股票数量过少: {len(stock_codes)}")

            except Exception as api_error:
                logger.error(f"从开盘啦API获取股票列表失败: {str(api_error)}")

            # 如果API获取失败，尝试从文件读取
            stock_list_files = ['stock_ranking_list.txt', 'stock_list.txt']
            for stock_list_file in stock_list_files:
                if os.path.exists(stock_list_file):
                    with open(stock_list_file, 'r', encoding='utf-8') as f:
                        stocks = [line.strip() for line in f if line.strip()]
                    if len(stocks) > 100:  # 确保文件中有足够的股票
                        logger.info(f"从 {stock_list_file} 获取到 {len(stocks)} 只股票")
                        return stocks

            # 最后的备用方案
            logger.warning("使用最小备用股票列表")
            return ['000001', '000002', '600000', '600036', '600519', '000858', '002415', '300059']

        except Exception as e:
            logger.error(f"获取股票列表失败: {str(e)}")
            return []
    
    @log_function_call
    def collect_stock_data(self, stock_code: str, trade_date: str) -> bool:
        """
        采集单只股票数据
        
        Args:
            stock_code: 股票代码
            trade_date: 交易日期
            
        Returns:
            bool: 采集是否成功
        """
        try:
            # 获取股票数据
            data = self.client.get_stock_data(stock_code, trade_date)
            if not data:
                return False
            
            # 解析并保存数据
            self._parse_and_save_stock_data(stock_code, trade_date, data)
            return True
            
        except Exception as e:
            logger.error(f"采集股票 {stock_code} 数据失败: {str(e)}")
            return False
    
    def _parse_and_save_stock_data(self, stock_code: str, trade_date: str, data: Dict[str, Any]):
        """
        解析并保存股票数据

        Args:
            stock_code: 股票代码
            trade_date: 交易日期
            data: API返回的数据
        """
        # 为每个线程创建独立的数据库会话
        session = get_session()
        try:
            # 解析基本行情数据
            if 'pankou' in data and 'real' in data['pankou']:
                real_data = data['pankou']['real']

                # 保存行情数据
                quote = StockQuote(
                    symbol=stock_code,
                    trade_date=trade_date,
                    current_price=real_data.get('last_px'),
                    open_price=real_data.get('open_px'),
                    high_price=real_data.get('high_px'),
                    low_price=real_data.get('low_px'),
                    close_price=real_data.get('last_px'),  # 当前价作为收盘价
                    volume=real_data.get('total_amount'),
                    amount=real_data.get('total_turnover'),
                    change_rate=real_data.get('px_change_rate'),
                    change_amount=real_data.get('px_change')
                )

                # 检查是否已存在
                existing = session.query(StockQuote).filter_by(
                    symbol=stock_code, trade_date=trade_date
                ).first()

                if not existing:
                    session.add(quote)

            # 解析标签数据
            if 'pankou' in data and 'tag' in data['pankou']:
                tag_content = data['pankou']['tag']
                if tag_content:
                    # 检查是否已存在
                    existing_tag = session.query(StockTag).filter_by(
                        symbol=stock_code, trade_date=trade_date, tag=tag_content
                    ).first()

                    if not existing_tag:
                        tag = StockTag(
                            symbol=stock_code,
                            tag=tag_content,
                            trade_date=trade_date
                        )
                        session.add(tag)

            # 解析概念数据
            if 'stockplate' in data:
                concepts = data['stockplate']
                for concept_info in concepts:
                    if isinstance(concept_info, list) and len(concept_info) > 0:
                        concept_name = concept_info[0]

                        # 检查是否已存在
                        existing_concept = session.query(StockConcept).filter_by(
                            symbol=stock_code, trade_date=trade_date, concept=concept_name
                        ).first()

                        if not existing_concept:
                            concept = StockConcept(
                                symbol=stock_code,
                                concept=concept_name,
                                trade_date=trade_date
                            )
                            session.add(concept)

            # 提交事务
            session.commit()
            logger.debug(f"股票 {stock_code} 数据保存成功")

        except Exception as e:
            session.rollback()
            logger.error(f"解析保存股票 {stock_code} 数据失败: {str(e)}")
            raise
        finally:
            session.close()
    
    @log_function_call
    def collect_plate_data(self, trade_date: str) -> bool:
        """
        采集板块数据
        
        Args:
            trade_date: 交易日期
            
        Returns:
            bool: 采集是否成功
        """
        try:
            logger.info(f"开始采集板块数据，日期: {trade_date}")

            # 获取板块数据（已通过F12抓包修复）
            # 转换日期格式：YYYYMMDD -> YYYY-MM-DD
            formatted_date = f"{trade_date[:4]}-{trade_date[4:6]}-{trade_date[6:8]}"
            data = self.client.get_plate_data(formatted_date)

            if not data:
                logger.warning("板块数据API返回空，尝试获取核心板块数据")
                # 尝试获取核心板块数据（不需要日期）
                data = self.client.get_core_plate_data()

            if not data:
                return False

            # 解析并保存板块数据
            self._parse_and_save_plate_data(trade_date, data)
            return True

        except Exception as e:
            logger.error(f"采集板块数据失败: {str(e)}")
            return False
    
    def _parse_and_save_plate_data(self, trade_date: str, data: Dict[str, Any]):
        """
        解析并保存板块数据
        
        Args:
            trade_date: 交易日期
            data: API返回的数据
        """
        try:
            # 根据F12抓包发现的实际数据结构进行解析
            # 板块数据结构: {'plates': {'list': [[code, name, ...], ...]}}

            if 'plates' in data and isinstance(data['plates'], dict):
                plates_data = data['plates']
                if 'list' in plates_data and isinstance(plates_data['list'], list):
                    plate_list = plates_data['list']

                    for plate_array in plate_list:
                        if isinstance(plate_array, list) and len(plate_array) >= 15:
                            # 根据实际数据结构解析
                            # ['886101', '兵装重组概念', 2031.473, 8.886, 0.006, ...]
                            plate_code = plate_array[0]
                            plate_name = plate_array[1]
                            plate_value = plate_array[2]  # 板块指数
                            change_rate = plate_array[3]  # 涨跌幅
                            change_amount = plate_array[4]  # 涨跌额

                            plate = PlateInfo(
                                plate_code=plate_code,
                                plate_name=plate_name,
                                plate_type='指数板块',  # 根据API类型设定
                                trade_date=trade_date,
                                change_rate=float(change_rate) if change_rate else 0.0,
                                stock_count=0,  # 这个API没有提供股票数量
                                up_count=0,     # 这个API没有提供上涨数量
                                down_count=0    # 这个API没有提供下跌数量
                            )

                            # 检查是否已存在
                            existing = self.session.query(PlateInfo).filter_by(
                                plate_code=plate_code, trade_date=trade_date
                            ).first()

                            if not existing:
                                self.session.add(plate)

                    logger.info(f"解析了 {len(plate_list)} 个板块数据")
                else:
                    logger.warning("板块数据中没有找到list字段或格式不正确")
            else:
                logger.warning("板块数据格式不正确，缺少plates字段")
            
            self.session.commit()
            logger.info("板块数据保存成功")
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"解析保存板块数据失败: {str(e)}")
            raise
    
    @log_function_call
    def collect_longhubang_data(self, trade_date: str) -> bool:
        """
        采集龙虎榜数据
        
        Args:
            trade_date: 交易日期
            
        Returns:
            bool: 采集是否成功
        """
        try:
            logger.info(f"开始采集龙虎榜数据，日期: {trade_date}")

            # 获取龙虎榜数据（已通过F12抓包修复）
            data = self.client.get_longhubang_data(trade_date)
            if not data:
                return False

            # 解析并保存龙虎榜数据
            self._parse_and_save_longhubang_data(trade_date, data)
            return True

        except Exception as e:
            logger.error(f"采集龙虎榜数据失败: {str(e)}")
            return False
    
    def _parse_and_save_longhubang_data(self, trade_date: str, data: Dict[str, Any]):
        """
        解析并保存龙虎榜数据
        """
        try:
            # 根据实际API返回结构解析数据
            if 'longhubang' in data:
                lhb_data = data['longhubang']
                for item in lhb_data:
                    lhb = LongHuBang(
                        symbol=item.get('symbol'),
                        trade_date=trade_date,
                        reason=item.get('reason'),
                        buy_amount=item.get('buy_amount'),
                        sell_amount=item.get('sell_amount'),
                        net_amount=item.get('net_amount'),
                        department=item.get('department')
                    )
                    
                    # 检查是否已存在
                    existing = self.session.query(LongHuBang).filter_by(
                        symbol=item.get('symbol'), trade_date=trade_date,
                        department=item.get('department')
                    ).first()
                    
                    if not existing:
                        self.session.add(lhb)
            
            self.session.commit()
            logger.info("龙虎榜数据保存成功")
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"解析保存龙虎榜数据失败: {str(e)}")
            raise
    
    @log_function_call
    def batch_collect_stocks(self, stock_list: List[str], trade_date: str, max_workers: int = 5) -> Dict[str, int]:
        """
        批量采集股票数据
        
        Args:
            stock_list: 股票代码列表
            trade_date: 交易日期
            max_workers: 最大并发数
            
        Returns:
            Dict: 采集结果统计
        """
        results = {'success': 0, 'failed': 0, 'total': len(stock_list)}
        
        logger.info(f"开始批量采集 {len(stock_list)} 只股票数据")
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交任务
            future_to_stock = {
                executor.submit(self.collect_stock_data, stock, trade_date): stock 
                for stock in stock_list
            }
            
            # 处理结果
            for future in as_completed(future_to_stock):
                stock = future_to_stock[future]
                try:
                    success = future.result()
                    if success:
                        results['success'] += 1
                    else:
                        results['failed'] += 1
                except Exception as e:
                    logger.error(f"股票 {stock} 采集异常: {str(e)}")
                    results['failed'] += 1
        
        logger.info(f"批量采集完成: 成功 {results['success']}, 失败 {results['failed']}")
        return results

    @log_function_call
    def collect_plate_strength_data(self, trade_date: str) -> bool:
        """
        采集板块强度数据
        包含强度、涨速、成交额、主力净额、主力买入、主力卖出、量比、流通市值等关键指标

        Args:
            trade_date: 交易日期

        Returns:
            bool: 是否成功
        """
        try:
            logger.info(f"开始采集板块强度数据，日期: {trade_date}")

            # 获取板块强度数据
            data = self.client.get_plate_strength_data()

            if data:
                self._parse_and_save_plate_strength_data(trade_date, data)
                logger.info(f"板块强度数据采集完成，日期: {trade_date}")
                return True
            else:
                logger.warning(f"板块强度数据为空，日期: {trade_date}")
                return False

        except Exception as e:
            logger.error(f"采集板块强度数据失败: {str(e)}")
            return False

    def _parse_and_save_plate_strength_data(self, trade_date: str, data: Dict[str, Any]):
        """
        解析并保存板块强度数据

        Args:
            trade_date: 交易日期
            data: API返回的数据
        """
        from models.database import PlateStrength
        from sqlalchemy import create_engine
        from sqlalchemy.orm import sessionmaker
        from config import Config

        # 创建独立的数据库会话
        engine = create_engine(Config.DATABASE_URL)
        Session = sessionmaker(bind=engine)
        session = Session()

        try:
            if 'plates' in data and isinstance(data['plates'], dict):
                plates_data = data['plates']
                if 'list' in plates_data and isinstance(plates_data['list'], list):
                    plate_list = plates_data['list']
                    saved_count = 0

                    for plate_array in plate_list:
                        if isinstance(plate_array, list) and len(plate_array) >= 13:
                            # 根据实际数据结构解析字段
                            plate_code = str(plate_array[0])
                            plate_name = str(plate_array[1])
                            strength = float(plate_array[2]) if plate_array[2] is not None else None
                            rise_speed = float(plate_array[3]) if plate_array[3] is not None else None
                            # plate_array[4] 可能是涨跌额，暂时跳过
                            turnover = float(plate_array[5]) if plate_array[5] is not None else None
                            main_net_amount = float(plate_array[6]) if plate_array[6] is not None else None
                            main_buy_amount = float(plate_array[7]) if plate_array[7] is not None else None
                            main_sell_amount = float(plate_array[8]) if plate_array[8] is not None else None
                            volume_ratio = float(plate_array[9]) if plate_array[9] is not None else None
                            market_cap = float(plate_array[10]) if plate_array[10] is not None else None

                            # 检查是否已存在相同记录
                            existing = session.query(PlateStrength).filter_by(
                                plate_code=plate_code,
                                trade_date=trade_date
                            ).first()

                            if not existing:
                                plate_strength = PlateStrength(
                                    plate_code=plate_code,
                                    plate_name=plate_name,
                                    strength=strength,
                                    rise_speed=rise_speed,
                                    turnover=turnover,
                                    main_net_amount=main_net_amount,
                                    main_buy_amount=main_buy_amount,
                                    main_sell_amount=main_sell_amount,
                                    volume_ratio=volume_ratio,
                                    market_cap=market_cap,
                                    trade_date=trade_date
                                )

                                session.add(plate_strength)
                                saved_count += 1
                            else:
                                logger.debug(f"板块强度数据已存在: {plate_code} - {trade_date}")

                    session.commit()
                    logger.info(f"板块强度数据保存完成，新增 {saved_count} 条记录")
                else:
                    logger.warning("板块强度数据格式不正确：缺少list字段")
            else:
                logger.warning("板块强度数据格式不正确：缺少plates字段")

        except Exception as e:
            session.rollback()
            logger.error(f"保存板块强度数据失败: {str(e)}")
            raise
        finally:
            session.close()
