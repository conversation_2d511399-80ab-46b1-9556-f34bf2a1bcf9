"""
数据处理和清洗模块
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from sqlalchemy import text

from models.database import get_session, get_engine, StockInfo, StockQuote, StockTag, StockConcept, PlateInfo, LongHuBang
from utils.logger import setup_logger, log_function_call
from config import get_config

config = get_config()
logger = setup_logger(__name__)

class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        self.session = get_session()
        self.engine = get_engine()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
    
    def close(self):
        """关闭资源"""
        if hasattr(self, 'session'):
            self.session.close()
    
    @log_function_call
    def clean_stock_data(self, trade_date: str = None) -> Dict[str, int]:
        """
        清洗股票数据
        
        Args:
            trade_date: 指定日期，None表示清洗所有数据
            
        Returns:
            Dict: 清洗结果统计
        """
        results = {'cleaned': 0, 'removed': 0}
        
        try:
            logger.info(f"开始清洗股票数据，日期: {trade_date or '全部'}")
            
            # 构建查询条件
            query = self.session.query(StockQuote)
            if trade_date:
                query = query.filter(StockQuote.trade_date == trade_date)
            
            # 获取需要清洗的数据
            quotes = query.all()
            
            for quote in quotes:
                cleaned = False
                
                # 清洗价格数据
                if self._clean_price_data(quote):
                    cleaned = True
                
                # 清洗成交量数据
                if self._clean_volume_data(quote):
                    cleaned = True
                
                # 检查数据有效性
                if not self._validate_quote_data(quote):
                    self.session.delete(quote)
                    results['removed'] += 1
                    continue
                
                if cleaned:
                    results['cleaned'] += 1
            
            self.session.commit()
            logger.info(f"数据清洗完成: 清洗 {results['cleaned']} 条，删除 {results['removed']} 条")
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"数据清洗失败: {str(e)}")
            raise
        
        return results
    
    def _clean_price_data(self, quote: StockQuote) -> bool:
        """
        清洗价格数据
        
        Args:
            quote: 股票行情对象
            
        Returns:
            bool: 是否进行了清洗
        """
        cleaned = False
        
        # 处理空值
        price_fields = ['current_price', 'open_price', 'high_price', 'low_price', 'close_price']
        for field in price_fields:
            value = getattr(quote, field)
            if value is None or value <= 0:
                setattr(quote, field, None)
                cleaned = True
        
        # 检查价格逻辑关系
        if (quote.high_price and quote.low_price and 
            quote.high_price < quote.low_price):
            # 交换高低价
            quote.high_price, quote.low_price = quote.low_price, quote.high_price
            cleaned = True
        
        # 检查异常价格（涨跌幅超过20%的数据需要特别关注）
        if (quote.current_price and quote.open_price and 
            abs(quote.current_price - quote.open_price) / quote.open_price > 0.2):
            logger.warning(f"股票 {quote.symbol} 价格异常: 当前价 {quote.current_price}, 开盘价 {quote.open_price}")
        
        return cleaned
    
    def _clean_volume_data(self, quote: StockQuote) -> bool:
        """
        清洗成交量数据
        
        Args:
            quote: 股票行情对象
            
        Returns:
            bool: 是否进行了清洗
        """
        cleaned = False
        
        # 处理负数成交量
        if quote.volume and quote.volume < 0:
            quote.volume = abs(quote.volume)
            cleaned = True
        
        if quote.amount and quote.amount < 0:
            quote.amount = abs(quote.amount)
            cleaned = True
        
        # 检查成交量和成交额的一致性
        if (quote.volume and quote.amount and quote.current_price and
            quote.volume > 0 and quote.amount > 0):
            # 计算平均价格
            avg_price = quote.amount / quote.volume
            if abs(avg_price - quote.current_price) / quote.current_price > 0.1:
                logger.warning(f"股票 {quote.symbol} 成交量价不匹配: 平均价 {avg_price}, 当前价 {quote.current_price}")
        
        return cleaned
    
    def _validate_quote_data(self, quote: StockQuote) -> bool:
        """
        验证行情数据有效性
        
        Args:
            quote: 股票行情对象
            
        Returns:
            bool: 数据是否有效
        """
        # 必须有股票代码和交易日期
        if not quote.symbol or not quote.trade_date:
            return False
        
        # 至少要有一个价格数据
        price_fields = [quote.current_price, quote.open_price, quote.high_price, quote.low_price, quote.close_price]
        if all(price is None for price in price_fields):
            return False
        
        return True
    
    @log_function_call
    def remove_duplicates(self, table_name: str, trade_date: str = None) -> int:
        """
        删除重复数据
        
        Args:
            table_name: 表名
            trade_date: 指定日期
            
        Returns:
            int: 删除的重复记录数
        """
        try:
            logger.info(f"开始删除表 {table_name} 的重复数据")
            
            if table_name == 'stock_quote':
                # 删除股票行情重复数据
                sql = """
                DELETE t1 FROM stock_quote t1
                INNER JOIN stock_quote t2 
                WHERE t1.id > t2.id 
                AND t1.symbol = t2.symbol 
                AND t1.trade_date = t2.trade_date
                """
                if trade_date:
                    sql += f" AND t1.trade_date = '{trade_date}'"
                    
            elif table_name == 'stock_tag':
                sql = """
                DELETE t1 FROM stock_tag t1
                INNER JOIN stock_tag t2 
                WHERE t1.id > t2.id 
                AND t1.symbol = t2.symbol 
                AND t1.trade_date = t2.trade_date
                AND t1.tag = t2.tag
                """
                if trade_date:
                    sql += f" AND t1.trade_date = '{trade_date}'"
                    
            elif table_name == 'stock_concept':
                sql = """
                DELETE t1 FROM stock_concept t1
                INNER JOIN stock_concept t2 
                WHERE t1.id > t2.id 
                AND t1.symbol = t2.symbol 
                AND t1.trade_date = t2.trade_date
                AND t1.concept = t2.concept
                """
                if trade_date:
                    sql += f" AND t1.trade_date = '{trade_date}'"
            else:
                logger.warning(f"不支持的表名: {table_name}")
                return 0
            
            result = self.session.execute(text(sql))
            deleted_count = result.rowcount
            self.session.commit()
            
            logger.info(f"表 {table_name} 删除重复数据 {deleted_count} 条")
            return deleted_count
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"删除重复数据失败: {str(e)}")
            return 0
    
    @log_function_call
    def export_to_excel(self, trade_date: str, output_path: str = None) -> str:
        """
        导出数据到Excel
        
        Args:
            trade_date: 交易日期
            output_path: 输出路径
            
        Returns:
            str: 输出文件路径
        """
        try:
            if not output_path:
                output_path = f"data_export_{trade_date}.xlsx"
            
            logger.info(f"开始导出数据到Excel: {output_path}")
            
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 导出股票行情数据
                quote_df = pd.read_sql(
                    f"SELECT * FROM stock_quote WHERE trade_date = '{trade_date}'",
                    self.engine
                )
                if not quote_df.empty:
                    quote_df.to_excel(writer, sheet_name='股票行情', index=False)
                
                # 导出股票标签数据
                tag_df = pd.read_sql(
                    f"SELECT * FROM stock_tag WHERE trade_date = '{trade_date}'",
                    self.engine
                )
                if not tag_df.empty:
                    tag_df.to_excel(writer, sheet_name='股票标签', index=False)
                
                # 导出股票概念数据
                concept_df = pd.read_sql(
                    f"SELECT * FROM stock_concept WHERE trade_date = '{trade_date}'",
                    self.engine
                )
                if not concept_df.empty:
                    concept_df.to_excel(writer, sheet_name='股票概念', index=False)
                
                # 导出板块数据
                plate_df = pd.read_sql(
                    f"SELECT * FROM plate_info WHERE trade_date = '{trade_date}'",
                    self.engine
                )
                if not plate_df.empty:
                    plate_df.to_excel(writer, sheet_name='板块信息', index=False)
                
                # 导出龙虎榜数据
                lhb_df = pd.read_sql(
                    f"SELECT * FROM long_hu_bang WHERE trade_date = '{trade_date}'",
                    self.engine
                )
                if not lhb_df.empty:
                    lhb_df.to_excel(writer, sheet_name='龙虎榜', index=False)
            
            logger.info(f"数据导出完成: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"导出数据到Excel失败: {str(e)}")
            raise
    
    @log_function_call
    def get_data_statistics(self, trade_date: str = None) -> Dict[str, Any]:
        """
        获取数据统计信息
        
        Args:
            trade_date: 指定日期
            
        Returns:
            Dict: 统计信息
        """
        try:
            stats = {}
            
            # 股票行情统计
            query = "SELECT COUNT(*) as count FROM stock_quote"
            if trade_date:
                query += f" WHERE trade_date = '{trade_date}'"
            result = self.session.execute(text(query)).fetchone()
            stats['stock_quote_count'] = result[0] if result else 0
            
            # 股票标签统计
            query = "SELECT COUNT(*) as count FROM stock_tag"
            if trade_date:
                query += f" WHERE trade_date = '{trade_date}'"
            result = self.session.execute(text(query)).fetchone()
            stats['stock_tag_count'] = result[0] if result else 0
            
            # 股票概念统计
            query = "SELECT COUNT(*) as count FROM stock_concept"
            if trade_date:
                query += f" WHERE trade_date = '{trade_date}'"
            result = self.session.execute(text(query)).fetchone()
            stats['stock_concept_count'] = result[0] if result else 0
            
            # 板块信息统计
            query = "SELECT COUNT(*) as count FROM plate_info"
            if trade_date:
                query += f" WHERE trade_date = '{trade_date}'"
            result = self.session.execute(text(query)).fetchone()
            stats['plate_info_count'] = result[0] if result else 0
            
            # 龙虎榜统计
            query = "SELECT COUNT(*) as count FROM long_hu_bang"
            if trade_date:
                query += f" WHERE trade_date = '{trade_date}'"
            result = self.session.execute(text(query)).fetchone()
            stats['long_hu_bang_count'] = result[0] if result else 0
            
            logger.info(f"数据统计完成: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"获取数据统计失败: {str(e)}")
            return {}
