#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试API响应数据
"""

import json
from core.http_client import KaipanlaClient

def debug_api():
    """调试API响应"""
    client = KaipanlaClient()
    
    try:
        # 测试股票数据API
        print("=== 测试股票数据API ===")
        stock_data = client.get_stock_data("000001", "20250630")
        print(f"股票数据类型: {type(stock_data)}")
        print(f"股票数据内容: {json.dumps(stock_data, indent=2, ensure_ascii=False)}")
        
        print("\n=== 测试板块数据API ===")
        plate_data = client.get_plate_data("20250630")
        print(f"板块数据类型: {type(plate_data)}")
        print(f"板块数据内容: {json.dumps(plate_data, indent=2, ensure_ascii=False)}")
        
        print("\n=== 测试龙虎榜数据API ===")
        lhb_data = client.get_longhubang_data("20250630")
        print(f"龙虎榜数据类型: {type(lhb_data)}")
        print(f"龙虎榜数据内容: {json.dumps(lhb_data, indent=2, ensure_ascii=False)}")
        
    except Exception as e:
        print(f"调试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        client.close()

if __name__ == "__main__":
    debug_api()
