"""
任务调度模块
"""
import schedule
import time
import threading
from datetime import datetime, timedelta
from typing import Callable, Dict, Any, List
from concurrent.futures import ThreadPoolExecutor

from core.data_collector import DataCollector
from core.data_processor import DataProcessor
from models.database import init_database
from utils.logger import setup_logger, log_function_call
from utils.error_handler import retry_on_exception, global_error_collector
from config import get_config

config = get_config()
logger = setup_logger(__name__)

class TaskScheduler:
    """任务调度器"""
    
    def __init__(self):
        self.is_running = False
        self.scheduler_thread = None
        self.tasks = {}
        self.executor = ThreadPoolExecutor(max_workers=3)
        
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.stop()
    
    @log_function_call
    def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("调度器已经在运行中")
            return
        
        logger.info("启动任务调度器")
        self.is_running = True
        
        # 初始化数据库
        init_database()
        
        # 设置定时任务
        self._setup_scheduled_tasks()
        
        # 启动调度线程
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        logger.info("任务调度器启动成功")
    
    def stop(self):
        """停止调度器"""
        if not self.is_running:
            return
        
        logger.info("停止任务调度器")
        self.is_running = False
        
        # 清空所有任务
        schedule.clear()
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        logger.info("任务调度器已停止")
    
    def _setup_scheduled_tasks(self):
        """设置定时任务"""
        # 每日数据采集任务 - 工作日上午9:30
        schedule.every().monday.at("09:30").do(self._schedule_daily_collection)
        schedule.every().tuesday.at("09:30").do(self._schedule_daily_collection)
        schedule.every().wednesday.at("09:30").do(self._schedule_daily_collection)
        schedule.every().thursday.at("09:30").do(self._schedule_daily_collection)
        schedule.every().friday.at("09:30").do(self._schedule_daily_collection)
        
        # 实时数据采集任务 - 工作日交易时间内每5分钟
        schedule.every(5).minutes.do(self._schedule_realtime_collection)
        
        # 数据清洗任务 - 每日收盘后
        schedule.every().monday.at("15:30").do(self._schedule_data_cleaning)
        schedule.every().tuesday.at("15:30").do(self._schedule_data_cleaning)
        schedule.every().wednesday.at("15:30").do(self._schedule_data_cleaning)
        schedule.every().thursday.at("15:30").do(self._schedule_data_cleaning)
        schedule.every().friday.at("15:30").do(self._schedule_data_cleaning)
        
        # 数据导出任务 - 每日晚上8点
        schedule.every().day.at("20:00").do(self._schedule_data_export)
        
        # 系统维护任务 - 每周日凌晨2点
        schedule.every().sunday.at("02:00").do(self._schedule_maintenance)
        
        logger.info("定时任务设置完成")
    
    def _run_scheduler(self):
        """运行调度器主循环"""
        logger.info("调度器主循环开始")
        
        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(1)
            except Exception as e:
                logger.error(f"调度器运行异常: {str(e)}")
                global_error_collector.add_error(f"调度器异常: {str(e)}")
                time.sleep(5)  # 异常后等待5秒再继续
        
        logger.info("调度器主循环结束")
    
    def _schedule_daily_collection(self):
        """调度每日数据采集任务"""
        if not self._is_trading_time():
            logger.info("非交易时间，跳过数据采集")
            return
        
        logger.info("开始执行每日数据采集任务")
        future = self.executor.submit(self._daily_collection_task)
        self.tasks[f"daily_collection_{datetime.now().strftime('%Y%m%d_%H%M%S')}"] = future
    
    def _schedule_realtime_collection(self):
        """调度实时数据采集任务"""
        if not self._is_trading_time():
            return
        
        logger.info("开始执行实时数据采集任务")
        future = self.executor.submit(self._realtime_collection_task)
        self.tasks[f"realtime_collection_{datetime.now().strftime('%Y%m%d_%H%M%S')}"] = future
    
    def _schedule_data_cleaning(self):
        """调度数据清洗任务"""
        logger.info("开始执行数据清洗任务")
        future = self.executor.submit(self._data_cleaning_task)
        self.tasks[f"data_cleaning_{datetime.now().strftime('%Y%m%d_%H%M%S')}"] = future
    
    def _schedule_data_export(self):
        """调度数据导出任务"""
        logger.info("开始执行数据导出任务")
        future = self.executor.submit(self._data_export_task)
        self.tasks[f"data_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}"] = future
    
    def _schedule_maintenance(self):
        """调度系统维护任务"""
        logger.info("开始执行系统维护任务")
        future = self.executor.submit(self._maintenance_task)
        self.tasks[f"maintenance_{datetime.now().strftime('%Y%m%d_%H%M%S')}"] = future
    
    @retry_on_exception(max_attempts=3, delay=5.0)
    def _daily_collection_task(self):
        """每日数据采集任务"""
        try:
            trade_date = datetime.now().strftime('%Y%m%d')
            
            with DataCollector() as collector:
                # 获取股票列表
                stock_list = collector.get_stock_list_from_tushare()
                if not stock_list:
                    logger.warning("未获取到股票列表")
                    return
                
                # 批量采集股票数据
                results = collector.batch_collect_stocks(stock_list, trade_date)
                logger.info(f"每日数据采集完成: {results}")
                
                # 采集板块数据
                collector.collect_plate_data(trade_date)
                
                # 采集龙虎榜数据
                collector.collect_longhubang_data(trade_date)
                
        except Exception as e:
            logger.error(f"每日数据采集任务失败: {str(e)}")
            global_error_collector.add_error(f"每日数据采集失败: {str(e)}")
            raise
    
    @retry_on_exception(max_attempts=2, delay=2.0)
    def _realtime_collection_task(self):
        """实时数据采集任务"""
        try:
            trade_date = datetime.now().strftime('%Y%m%d')
            
            with DataCollector() as collector:
                # 获取重点关注股票列表（可以从配置文件或数据库读取）
                focus_stocks = ['000001', '000002', '600000', '600036', '600519']
                
                # 采集重点股票实时数据
                for stock in focus_stocks:
                    collector.collect_stock_data(stock, trade_date)
                
                logger.info(f"实时数据采集完成，采集股票数: {len(focus_stocks)}")
                
        except Exception as e:
            logger.error(f"实时数据采集任务失败: {str(e)}")
            global_error_collector.add_error(f"实时数据采集失败: {str(e)}")
            raise
    
    @retry_on_exception(max_attempts=2, delay=3.0)
    def _data_cleaning_task(self):
        """数据清洗任务"""
        try:
            trade_date = datetime.now().strftime('%Y%m%d')
            
            with DataProcessor() as processor:
                # 清洗当日数据
                clean_results = processor.clean_stock_data(trade_date)
                logger.info(f"数据清洗完成: {clean_results}")
                
                # 删除重复数据
                tables = ['stock_quote', 'stock_tag', 'stock_concept']
                for table in tables:
                    deleted = processor.remove_duplicates(table, trade_date)
                    logger.info(f"表 {table} 删除重复数据 {deleted} 条")
                
        except Exception as e:
            logger.error(f"数据清洗任务失败: {str(e)}")
            global_error_collector.add_error(f"数据清洗失败: {str(e)}")
            raise
    
    @retry_on_exception(max_attempts=2, delay=2.0)
    def _data_export_task(self):
        """数据导出任务"""
        try:
            trade_date = datetime.now().strftime('%Y%m%d')
            
            with DataProcessor() as processor:
                # 导出当日数据
                output_path = processor.export_to_excel(trade_date)
                logger.info(f"数据导出完成: {output_path}")
                
                # 获取数据统计
                stats = processor.get_data_statistics(trade_date)
                logger.info(f"数据统计: {stats}")
                
        except Exception as e:
            logger.error(f"数据导出任务失败: {str(e)}")
            global_error_collector.add_error(f"数据导出失败: {str(e)}")
            raise
    
    def _maintenance_task(self):
        """系统维护任务"""
        try:
            logger.info("开始系统维护")
            
            # 清理过期日志文件
            self._cleanup_old_logs()
            
            # 清理过期数据（保留最近30天）
            self._cleanup_old_data()
            
            # 数据库优化
            self._optimize_database()
            
            logger.info("系统维护完成")
            
        except Exception as e:
            logger.error(f"系统维护任务失败: {str(e)}")
            global_error_collector.add_error(f"系统维护失败: {str(e)}")
    
    def _cleanup_old_logs(self):
        """清理旧日志文件"""
        import os
        import glob
        
        log_dir = "logs"
        if not os.path.exists(log_dir):
            return
        
        # 删除7天前的日志文件
        cutoff_date = datetime.now() - timedelta(days=7)
        log_files = glob.glob(os.path.join(log_dir, "*.log*"))
        
        for log_file in log_files:
            try:
                file_time = datetime.fromtimestamp(os.path.getmtime(log_file))
                if file_time < cutoff_date:
                    os.remove(log_file)
                    logger.info(f"删除旧日志文件: {log_file}")
            except Exception as e:
                logger.warning(f"删除日志文件失败 {log_file}: {str(e)}")
    
    def _cleanup_old_data(self):
        """清理旧数据"""
        try:
            cutoff_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
            
            with DataProcessor() as processor:
                # 这里可以添加删除旧数据的逻辑
                # 例如：删除30天前的数据
                logger.info(f"清理 {cutoff_date} 之前的数据")
                
        except Exception as e:
            logger.error(f"清理旧数据失败: {str(e)}")
    
    def _optimize_database(self):
        """优化数据库"""
        try:
            with DataProcessor() as processor:
                # 执行数据库优化操作
                # 例如：重建索引、更新统计信息等
                logger.info("数据库优化完成")
                
        except Exception as e:
            logger.error(f"数据库优化失败: {str(e)}")
    
    def _is_trading_time(self) -> bool:
        """
        判断是否为交易时间
        
        Returns:
            bool: 是否为交易时间
        """
        now = datetime.now()
        
        # 检查是否为工作日
        if now.weekday() >= 5:  # 周六、周日
            return False
        
        # 检查是否在交易时间内
        current_time = now.time()
        morning_start = datetime.strptime("09:30", "%H:%M").time()
        morning_end = datetime.strptime("11:30", "%H:%M").time()
        afternoon_start = datetime.strptime("13:00", "%H:%M").time()
        afternoon_end = datetime.strptime("15:00", "%H:%M").time()
        
        return ((morning_start <= current_time <= morning_end) or 
                (afternoon_start <= current_time <= afternoon_end))
    
    def get_task_status(self) -> Dict[str, Any]:
        """
        获取任务状态
        
        Returns:
            Dict: 任务状态信息
        """
        status = {
            'is_running': self.is_running,
            'active_tasks': len([t for t in self.tasks.values() if not t.done()]),
            'completed_tasks': len([t for t in self.tasks.values() if t.done()]),
            'next_run_time': schedule.next_run(),
            'scheduled_jobs': len(schedule.jobs)
        }
        
        return status
