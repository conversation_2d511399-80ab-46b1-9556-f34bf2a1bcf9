#!/usr/bin/env python3
"""
测试修复后的板块API
"""
from core.http_client import KaipanlaClient

def test_plate_api():
    """测试修复后的板块API"""
    print("=== 测试修复后的板块API ===")
    
    client = KaipanlaClient()
    try:
        # 测试板块数据获取 - 使用YYYY-MM-DD格式
        data = client.get_plate_data('2025-06-30')
        
        print(f"板块数据类型: {type(data)}")
        
        if data:
            print("✅ 板块API调用成功！")
            print(f"数据键: {list(data.keys()) if isinstance(data, dict) else '非字典类型'}")
            
            # 显示数据内容
            if isinstance(data, dict):
                for key, value in data.items():
                    print(f"{key}: {type(value)}")
                    if isinstance(value, list) and len(value) > 0:
                        print(f"  {key} 样例 (前2个): {value[:2]}")
                    elif isinstance(value, str) and len(value) < 100:
                        print(f"  {key}: {value}")
            
            print(f"\n完整数据内容 (前500字符): {str(data)[:500]}...")
        else:
            print("❌ 板块API返回空数据")
            
        # 也测试一下YYYYMMDD格式
        print("\n=== 测试YYYYMMDD格式 ===")
        data2 = client.get_plate_data('20250630')
        if data2:
            print("✅ YYYYMMDD格式也成功！")
        else:
            print("❌ YYYYMMDD格式失败")
            
    except Exception as e:
        print(f"❌ 板块API调用失败: {e}")
    finally:
        client.close()

if __name__ == "__main__":
    test_plate_api()
