#!/usr/bin/env python3
"""
简单的数据查看脚本
"""
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from config import Config
from models.database import StockQuote, PlateInfo, LongHuBang, PlateStrength

def view_data():
    """查看数据"""
    print("🔍 === 开盘啦数据查看 ===\n")
    
    # 创建数据库连接
    engine = create_engine(Config.DATABASE_URL)
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # 1. 数据概览
        stock_count = session.query(StockQuote).count()
        plate_count = session.query(PlateInfo).count()
        lhb_count = session.query(LongHuBang).count()
        plate_strength_count = session.query(PlateStrength).count()

        print(f"📊 数据概览:")
        print(f"  📈 股票数据: {stock_count} 条")
        print(f"  📊 板块数据: {plate_count} 条")
        print(f"  🐉 龙虎榜数据: {lhb_count} 条")
        print(f"  💪 板块强度数据: {plate_strength_count} 条")
        print(f"  📅 总计: {stock_count + plate_count + lhb_count + plate_strength_count} 条数据\n")
        
        # 2. 显示最新股票数据
        if stock_count > 0:
            print("📈 === 最新股票数据 (前5条) ===")
            stocks = session.query(StockQuote).order_by(StockQuote.created_at.desc()).limit(5).all()
            for stock in stocks:
                print(f"  {stock.symbol} | {stock.trade_date} | 开盘:{stock.open_price} | 收盘:{stock.close_price} | 涨跌:{stock.change_rate}%")
            print()
        
        # 3. 显示最新板块数据
        if plate_count > 0:
            print("📊 === 最新板块数据 (前5条) ===")
            plates = session.query(PlateInfo).order_by(PlateInfo.change_rate.desc()).limit(5).all()
            for plate in plates:
                print(f"  {plate.plate_code} | {plate.plate_name} | {plate.trade_date} | 涨跌:{plate.change_rate}%")
            print()
        
        # 4. 显示最新龙虎榜数据
        if lhb_count > 0:
            print("🐉 === 最新龙虎榜数据 (前5条) ===")
            lhb_data = session.query(LongHuBang).order_by(LongHuBang.created_at.desc()).limit(5).all()
            for lhb in lhb_data:
                print(f"  {lhb.symbol} | {lhb.trade_date} | 原因:{lhb.reason} | 买入:{lhb.buy_amount}")
            print()

        # 5. 显示最新板块强度数据
        if plate_strength_count > 0:
            print("💪 === 最新板块强度数据 (前5条) ===")
            strength_data = session.query(PlateStrength).order_by(PlateStrength.strength.desc()).limit(5).all()
            for strength in strength_data:
                turnover_yi = strength.turnover/100000000 if strength.turnover else 0
                main_net_yi = strength.main_net_amount/100000000 if strength.main_net_amount else 0
                print(f"  {strength.plate_name} | 强度:{strength.strength:.2f} | 涨速:{strength.rise_speed:.2f}% | 成交额:{turnover_yi:.2f}亿 | 主力净额:{main_net_yi:.2f}亿")
            print()
        
        # 6. 显示最新数据日期
        print("📅 === 最新数据日期 ===")
        if stock_count > 0:
            latest_stock = session.query(StockQuote).order_by(StockQuote.trade_date.desc()).first()
            print(f"  📈 最新股票数据: {latest_stock.trade_date}")

        if plate_count > 0:
            latest_plate = session.query(PlateInfo).order_by(PlateInfo.trade_date.desc()).first()
            print(f"  📊 最新板块数据: {latest_plate.trade_date}")

        if lhb_count > 0:
            latest_lhb = session.query(LongHuBang).order_by(LongHuBang.trade_date.desc()).first()
            print(f"  🐉 最新龙虎榜数据: {latest_lhb.trade_date}")

        if plate_strength_count > 0:
            latest_strength = session.query(PlateStrength).order_by(PlateStrength.trade_date.desc()).first()
            print(f"  💪 最新板块强度数据: {latest_strength.trade_date}")
        
        print("\n✅ 数据查看完成！")
        
    except Exception as e:
        print(f"❌ 查看数据时发生错误: {e}")
    finally:
        session.close()

if __name__ == "__main__":
    view_data()
