"""
日志工具模块
"""
import logging
import os
from datetime import datetime
from config import get_config

config = get_config()

def setup_logger(name=__name__, log_file=None):
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        log_file: 日志文件路径
    
    Returns:
        logger: 配置好的日志记录器
    """
    logger = logging.getLogger(name)
    
    # 避免重复添加handler
    if logger.handlers:
        return logger
    
    logger.setLevel(getattr(logging, config.LOG_LEVEL))
    
    # 创建日志目录
    if log_file is None:
        log_file = config.LOG_FILE
    
    log_dir = os.path.dirname(log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 文件处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(getattr(logging, config.LOG_LEVEL))
    file_handler.setFormatter(formatter)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    
    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

def log_function_call(func):
    """
    装饰器：记录函数调用
    """
    def wrapper(*args, **kwargs):
        logger = setup_logger()
        logger.info(f"调用函数: {func.__name__}")
        try:
            result = func(*args, **kwargs)
            logger.info(f"函数 {func.__name__} 执行成功")
            return result
        except Exception as e:
            logger.error(f"函数 {func.__name__} 执行失败: {str(e)}")
            raise
    return wrapper

def log_api_request(url, params=None, response_status=None, error=None):
    """
    记录API请求日志
    
    Args:
        url: 请求URL
        params: 请求参数
        response_status: 响应状态码
        error: 错误信息
    """
    logger = setup_logger()
    
    if error:
        logger.error(f"API请求失败 - URL: {url}, 错误: {error}")
    else:
        logger.info(f"API请求成功 - URL: {url}, 状态码: {response_status}")
    
    if params:
        logger.debug(f"请求参数: {params}")

# 创建默认日志记录器
default_logger = setup_logger('kaipanla_spider')
