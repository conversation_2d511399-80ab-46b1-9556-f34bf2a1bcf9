"""
自定义异常类
"""

class KaipanlaSpiderException(Exception):
    """开盘啦爬虫基础异常类"""
    pass

class APIException(KaipanlaSpiderException):
    """API请求异常"""
    def __init__(self, message, status_code=None, response_text=None):
        super().__init__(message)
        self.status_code = status_code
        self.response_text = response_text

class DataParseException(KaipanlaSpiderException):
    """数据解析异常"""
    def __init__(self, message, raw_data=None):
        super().__init__(message)
        self.raw_data = raw_data

class DatabaseException(KaipanlaSpiderException):
    """数据库操作异常"""
    pass

class ConfigException(KaipanlaSpiderException):
    """配置异常"""
    pass

class NetworkException(KaipanlaSpiderException):
    """网络异常"""
    def __init__(self, message, url=None):
        super().__init__(message)
        self.url = url
