#!/usr/bin/env python3
"""
测试股票API响应格式
"""
from core.http_client import KaipanlaClient
import json

def test_known_stocks():
    """测试已知有效的股票代码"""
    client = KaipanlaClient()
    
    # 使用系统中已知有效的股票代码
    known_stocks = [
        '000001',  # 平安银行
        '000002',  # 万科A
        '600000',  # 浦发银行
        '600036',  # 招商银行
        '600519',  # 贵州茅台
        '000858',  # 五粮液
        '002415',  # 海康威视
        '300059',  # 东方财富
    ]
    
    try:
        for stock_code in known_stocks:
            print(f"\n🔍 测试股票: {stock_code}")
            
            # 测试获取股票数据
            data = client.get_stock_data(stock_code, '20250630')
            
            if data:
                print(f"✅ 获取到数据，类型: {type(data)}")
                print(f"📊 数据内容: {json.dumps(data, ensure_ascii=False, indent=2)}")
                
                # 检查数据结构
                if isinstance(data, dict):
                    print(f"🔑 数据键: {list(data.keys())}")
                    
                    # 检查是否有有效的股票信息
                    has_valid_data = any(key in data for key in ['name', 'close', 'open', 'high', 'low', 'volume'])
                    print(f"📈 包含有效股票数据: {has_valid_data}")
                    
                elif isinstance(data, list):
                    print(f"📋 数据列表长度: {len(data)}")
                    if len(data) > 0:
                        print(f"🔍 第一个元素: {json.dumps(data[0], ensure_ascii=False, indent=2)}")
                        
            else:
                print(f"❌ 未获取到数据")
                
            print("-" * 50)
            
    finally:
        client.close()

def test_api_raw_response():
    """测试API原始响应"""
    client = KaipanlaClient()
    
    try:
        # 直接测试API请求
        params = {
            'c': 'Stock',
            'a': 'GetStockData',
            'StockNo': '000001',
            'Time': '20250630',
            'UserID': client.user_id,
            'Token': client.token
        }
        
        print("🌐 测试原始API请求...")
        print(f"📤 请求参数: {params}")
        
        response = client.post('https://pchq.kaipanla.com/w1/api/index.php', data=params)
        
        if response:
            print(f"✅ 响应状态码: {response.status_code}")
            print(f"📥 响应内容: {response.text[:500]}...")
            
            try:
                json_data = response.json()
                print(f"📊 JSON数据: {json.dumps(json_data, ensure_ascii=False, indent=2)}")
            except:
                print("❌ 响应不是有效的JSON格式")
        else:
            print("❌ 请求失败")
            
    finally:
        client.close()

if __name__ == "__main__":
    print("🚀 === 测试股票API响应格式 ===\n")
    
    print("1️⃣ 测试已知股票代码...")
    test_known_stocks()
    
    print("\n2️⃣ 测试原始API响应...")
    test_api_raw_response()
