#!/usr/bin/env python3
"""
测试完整的数据收集系统
"""
from core.data_collector import DataCollector

def test_complete_system():
    """测试完整的数据收集系统"""
    print("🚀 === 测试完整的数据收集系统 ===")
    
    with DataCollector() as collector:
        results = {}
        
        print("\n1. 测试股票数据收集")
        success = collector.collect_stock_data('000001', '20250630')
        results['股票数据'] = success
        print(f"股票数据收集: {'✅ 成功' if success else '❌ 失败'}")
        
        print("\n2. 测试板块数据收集")
        success = collector.collect_plate_data('20250630')
        results['板块数据'] = success
        print(f"板块数据收集: {'✅ 成功' if success else '❌ 失败'}")
        
        print("\n3. 测试龙虎榜数据收集")
        success = collector.collect_longhubang_data('20250630')
        results['龙虎榜数据'] = success
        print(f"龙虎榜数据收集: {'✅ 成功' if success else '❌ 失败'}")
        
        # 总结
        success_count = sum(results.values())
        total_count = len(results)
        
        print(f"\n📊 === 数据收集系统测试总结 ===")
        print(f"成功: {success_count}/{total_count} 个数据类型")
        
        for data_type, success in results.items():
            status = "✅" if success else "❌"
            print(f"  {status} {data_type}")
        
        if success_count == total_count:
            print("\n🎉 所有数据收集功能都正常工作！")
        elif success_count >= 2:
            print("\n👍 大部分数据收集功能正常！")
        else:
            print("\n⚠️  数据收集系统需要进一步调试")

if __name__ == "__main__":
    test_complete_system()
