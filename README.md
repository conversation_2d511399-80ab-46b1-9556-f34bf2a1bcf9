# 开盘啦数据采集项目

一个完整的开盘啦股票数据采集系统，支持板块列表、个股列表、龙虎榜等数据的自动化采集、清洗和存储。

## 🚀 功能特性

- **全面数据采集**: 支持股票行情、板块信息、概念标签、龙虎榜等多维度数据
- **智能调度系统**: 基于时间的自动化任务调度，支持实时和定时采集
- **数据清洗**: 自动数据验证、去重和异常处理
- **多种存储**: 支持SQLite、MySQL等数据库，可导出Excel格式
- **健壮性设计**: 完善的错误处理、重试机制和日志系统
- **易于扩展**: 模块化设计，便于添加新的数据源和功能

## 📋 系统要求

- Python 3.8+
- 支持的数据库: SQLite (默认) / MySQL
- 内存: 建议2GB以上
- 磁盘: 建议预留5GB以上空间

## 🛠 安装配置

### 1. 克隆项目
```bash
git clone <repository-url>
cd kaipanla-spider
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置环境
```bash
# 复制配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

### 4. 初始化数据库
```bash
python main.py init
```

## 🎯 快速开始

### 测试连接
```bash
# 测试API连接
python main.py test

# 运行基础测试
python test_spider.py basic
```

### 数据采集
```bash
# 采集今日数据
python main.py collect

# 清洗数据
python main.py clean

# 导出数据
python main.py export --output data_export.xlsx
```

### 启动调度器
```bash
# 启动自动化调度系统
python main.py start
```

## 📊 数据结构

### 股票行情数据 (stock_quote)
- symbol: 股票代码
- trade_date: 交易日期
- current_price: 当前价格
- open_price: 开盘价
- high_price: 最高价
- low_price: 最低价
- volume: 成交量
- amount: 成交额

### 股票标签数据 (stock_tag)
- symbol: 股票代码
- tag: 标签内容
- trade_date: 交易日期

### 股票概念数据 (stock_concept)
- symbol: 股票代码
- concept: 概念名称
- trade_date: 交易日期

### 板块信息数据 (plate_info)
- plate_code: 板块代码
- plate_name: 板块名称
- change_rate: 板块涨跌幅
- stock_count: 板块股票数量

### 龙虎榜数据 (long_hu_bang)
- symbol: 股票代码
- trade_date: 交易日期
- reason: 上榜原因
- buy_amount: 买入金额
- sell_amount: 卖出金额
- department: 营业部

## ⚙️ 配置说明

### 主要配置项 (config.py)
```python
# API配置
KAIPANLA_API_BASE = "https://pchq.kaipanla.com/w1/api/index.php"
KAIPANLA_LHB_API = "https://pclhb.kaipanla.com/w1/api/index.php"

# 数据库配置
DATABASE_URL = "sqlite:///kaipanla_data.db"  # SQLite
# DATABASE_URL = "mysql+pymysql://user:pass@host:port/db"  # MySQL

# 请求配置
REQUEST_TIMEOUT = 30
REQUEST_RETRY_TIMES = 3
REQUEST_DELAY = 1  # 请求间隔秒数
```

### 调度配置
- **每日数据采集**: 工作日上午9:30
- **实时数据采集**: 交易时间内每5分钟
- **数据清洗**: 每日收盘后15:30
- **数据导出**: 每日晚上20:00
- **系统维护**: 每周日凌晨2:00

## 🔧 命令行工具

```bash
# 显示帮助
python main.py -h

# 初始化数据库
python main.py init

# 测试API连接
python main.py test

# 采集今日数据
python main.py collect

# 清洗指定日期数据
python main.py clean --date 20231201

# 导出数据到Excel
python main.py export --date 20231201 --output report.xlsx

# 显示系统状态
python main.py status

# 启动调度器
python main.py start
```

## 📝 日志系统

日志文件位置: `logs/kaipanla_spider.log`

日志级别:
- DEBUG: 详细调试信息
- INFO: 一般信息
- WARNING: 警告信息
- ERROR: 错误信息

## 🛡️ 错误处理

系统包含完善的错误处理机制:

- **重试机制**: 网络请求失败自动重试
- **熔断器**: 防止系统过载
- **异常捕获**: 全面的异常处理和日志记录
- **数据验证**: 自动数据完整性检查

## 📈 性能优化

- **并发采集**: 支持多线程并发数据采集
- **批量处理**: 批量数据库操作提高效率
- **连接池**: 数据库连接池管理
- **缓存机制**: 减少重复请求

## 🔍 监控和维护

### 系统状态监控
```bash
python main.py status
```

### 数据统计
- 实时数据量统计
- 采集成功率监控
- 错误统计和分析

### 自动维护
- 定期清理过期日志
- 数据库优化
- 过期数据清理

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## ⚠️ 免责声明

本项目仅用于学习和研究目的。使用本项目进行数据采集时，请遵守相关网站的使用条款和法律法规。作者不承担因使用本项目而产生的任何法律责任。

## 📞 支持

如有问题或建议，请通过以下方式联系:

- 提交 Issue
- 发送邮件
- 加入讨论群

---

**注意**: 使用前请确保已获得数据源网站的授权，并遵守相关的使用协议。
