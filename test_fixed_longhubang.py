#!/usr/bin/env python3
"""
测试修复后的龙虎榜API
"""
from core.http_client import KaipanlaClient

def test_longhubang_api():
    """测试修复后的龙虎榜API"""
    print("=== 测试修复后的龙虎榜API ===")
    
    client = KaipanlaClient()
    try:
        # 测试龙虎榜数据获取
        data = client.get_longhubang_data('20250630')
        
        print(f"龙虎榜数据类型: {type(data)}")
        
        if data:
            print("✅ 龙虎榜API调用成功！")
            print(f"数据键: {list(data.keys()) if isinstance(data, dict) else '非字典类型'}")
            
            # 显示数据内容
            if isinstance(data, dict):
                for key, value in data.items():
                    print(f"{key}: {type(value)}")
                    if isinstance(value, list) and len(value) > 0:
                        print(f"  {key} 样例 (前3个): {value[:3]}")
                    elif isinstance(value, str) and len(value) < 100:
                        print(f"  {key}: {value}")
            
            print(f"\n完整数据内容 (前500字符): {str(data)[:500]}...")
        else:
            print("❌ 龙虎榜API返回空数据")
            
    except Exception as e:
        print(f"❌ 龙虎榜API调用失败: {e}")
    finally:
        client.close()

if __name__ == "__main__":
    test_longhubang_api()
