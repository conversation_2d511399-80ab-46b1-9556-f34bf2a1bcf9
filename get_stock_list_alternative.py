#!/usr/bin/env python3
"""
通过其他方式获取股票列表
"""
import requests
import json
from typing import List

def get_stock_list_from_sina() -> List[str]:
    """从新浪财经获取股票列表"""
    try:
        print("📡 尝试从新浪财经获取股票列表...")
        
        # 新浪财经的股票列表API
        urls = [
            'http://hq.sinajs.cn/list=s_sh000001,s_sz399001',  # 测试连接
            'http://money.finance.sina.com.cn/quotes_service/api/json_v2.php/Market_Center.getHQNodeData?page=1&num=5000&sort=symbol&asc=1&node=hs_a',  # A股列表
        ]
        
        for url in urls:
            try:
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    print(f"✅ 成功访问: {url}")
                    print(f"   响应长度: {len(response.text)}")
                    print(f"   响应前200字符: {response.text[:200]}")
                    
                    # 尝试解析JSON
                    if 'json' in url:
                        try:
                            data = response.json()
                            if isinstance(data, list) and len(data) > 0:
                                print(f"   获取到 {len(data)} 条股票数据")
                                # 提取股票代码
                                stock_codes = []
                                for item in data[:10]:  # 显示前10个示例
                                    if isinstance(item, dict) and 'symbol' in item:
                                        stock_codes.append(item['symbol'])
                                        print(f"      {item['symbol']}: {item.get('name', 'N/A')}")
                                return [item['symbol'] for item in data if isinstance(item, dict) and 'symbol' in item]
                        except:
                            print("   JSON解析失败")
                else:
                    print(f"❌ 访问失败: {url}, 状态码: {response.status_code}")
            except Exception as e:
                print(f"❌ 请求异常: {url}, 错误: {e}")
        
        return []
    except Exception as e:
        print(f"❌ 获取股票列表失败: {e}")
        return []

def get_stock_list_from_eastmoney() -> List[str]:
    """从东方财富获取股票列表（分页获取）"""
    try:
        print("📡 尝试从东方财富获取股票列表...")

        all_stocks = []
        page = 1
        page_size = 1000

        while True:
            print(f"   获取第 {page} 页...")

            # 东方财富的股票列表API
            url = 'http://80.push2.eastmoney.com/api/qt/clist/get'
            params = {
                'pn': str(page),
                'pz': str(page_size),
                'po': '1',
                'np': '1',
                'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                'fltt': '2',
                'invt': '2',
                'fid': 'f3',
                'fs': 'm:0 t:6,m:0 t:13,m:0 t:80,m:1 t:2,m:1 t:23,m:0 t:81 s:2048',  # A股
                'fields': 'f12,f14'  # 只要代码和名称
            }

            response = requests.get(url, params=params, timeout=15)
            if response.status_code == 200:
                try:
                    data = response.json()
                    if 'data' in data and 'diff' in data['data']:
                        stocks = data['data']['diff']
                        if not stocks:  # 没有更多数据
                            break

                        print(f"      第 {page} 页获取到 {len(stocks)} 只股票")

                        # 显示前几个示例（仅第一页）
                        if page == 1:
                            for i, stock in enumerate(stocks[:10]):
                                if 'f12' in stock and 'f14' in stock:
                                    code = stock['f12']
                                    name = stock['f14']
                                    print(f"      {code}: {name}")

                        # 收集股票代码
                        page_codes = [stock['f12'] for stock in stocks if 'f12' in stock]
                        all_stocks.extend(page_codes)

                        # 如果这一页数据少于page_size，说明是最后一页
                        if len(stocks) < page_size:
                            break

                        page += 1

                        # 安全限制：最多获取10页
                        if page > 10:
                            break

                    else:
                        print("❌ 数据格式不正确")
                        break
                except Exception as e:
                    print(f"❌ JSON解析失败: {e}")
                    break
            else:
                print(f"❌ 请求失败，状态码: {response.status_code}")
                break

        print(f"✅ 总共获取到 {len(all_stocks)} 只股票")
        return all_stocks

    except Exception as e:
        print(f"❌ 获取股票列表失败: {e}")
        return []

def generate_stock_codes() -> List[str]:
    """生成常见的股票代码范围"""
    print("🔢 生成常见股票代码范围...")
    
    stock_codes = []
    
    # 深圳主板 000001-000999
    for i in range(1, 1000):
        stock_codes.append(f"000{i:03d}")
    
    # 深圳中小板 002001-002999  
    for i in range(1, 1000):
        stock_codes.append(f"002{i:03d}")
    
    # 深圳创业板 300001-300999
    for i in range(1, 1000):
        stock_codes.append(f"300{i:03d}")
    
    # 上海主板 600000-603999
    for i in range(600000, 604000):
        stock_codes.append(f"{i}")
    
    # 上海科创板 688001-688999
    for i in range(1, 1000):
        stock_codes.append(f"688{i:03d}")
    
    print(f"✅ 生成了 {len(stock_codes)} 个股票代码")
    return stock_codes

def main():
    """主函数"""
    print("🚀 === 获取A股股票列表 ===\n")
    
    # 方法1: 从东方财富获取
    stock_list = get_stock_list_from_eastmoney()
    
    if not stock_list:
        # 方法2: 从新浪财经获取
        stock_list = get_stock_list_from_sina()
    
    if not stock_list:
        # 方法3: 生成常见股票代码
        stock_list = generate_stock_codes()
    
    if stock_list:
        print(f"\n🎯 === 最终结果 ===")
        print(f"✅ 获取到 {len(stock_list)} 只股票代码")
        print(f"📝 前20个股票代码: {stock_list[:20]}")
        
        # 保存到文件
        with open('stock_list.txt', 'w', encoding='utf-8') as f:
            for code in stock_list:
                f.write(f"{code}\n")
        print(f"💾 股票列表已保存到 stock_list.txt")
        
        return stock_list
    else:
        print("❌ 未能获取到股票列表")
        return []

if __name__ == "__main__":
    main()
