#!/usr/bin/env python3
"""
探索开盘啦股票列表API
"""
from core.http_client import KaipanlaClient
import json

def explore_stock_list_api():
    """探索获取股票列表的API"""
    print("🔍 === 探索开盘啦股票列表API ===\n")
    
    client = KaipanlaClient()
    try:
        # 尝试不同的API参数来获取股票列表
        test_params = [
            # 1. 尝试获取股票列表的可能参数
            {'c': 'StockList', 'a': 'GetAllStocks'},
            {'c': 'Stock', 'a': 'GetStockList'},
            {'c': 'PCArrangeData', 'a': 'GetStockList'},
            {'c': 'StockData', 'a': 'GetList'},
            
            # 2. 尝试类似板块的参数结构
            {'c': 'PCArrangeData', 'a': 'GetZSIndexStock', 'SelType': '1', 'ZSType': '1', 'PType': '1', 'POrder': '0', 'PIndex': '0', 'Pst': '100'},
            
            # 3. 尝试不同的ZSType值（可能代表不同市场）
            {'c': 'PCArrangeData', 'a': 'GetZSIndexStock', 'SelType': '2', 'ZSType': '1', 'PType': '1', 'POrder': '0', 'PIndex': '0', 'Pst': '300'},
            {'c': 'PCArrangeData', 'a': 'GetZSIndexStock', 'SelType': '2', 'ZSType': '2', 'PType': '1', 'POrder': '0', 'PIndex': '0', 'Pst': '300'},
            {'c': 'PCArrangeData', 'a': 'GetZSIndexStock', 'SelType': '2', 'ZSType': '3', 'PType': '1', 'POrder': '0', 'PIndex': '0', 'Pst': '300'},
            
            # 4. 尝试其他可能的action
            {'c': 'PCArrangeData', 'a': 'GetStockArrange', 'SelType': '2', 'ZSType': '1', 'PType': '1', 'POrder': '0', 'PIndex': '0', 'Pst': '300'},
            {'c': 'PCArrangeData', 'a': 'GetAllStock', 'SelType': '2', 'ZSType': '1', 'PType': '1', 'POrder': '0', 'PIndex': '0', 'Pst': '300'},
        ]
        
        for i, params in enumerate(test_params, 1):
            print(f"{i}. 测试参数: {params}")
            try:
                # 添加认证信息
                full_params = params.copy()
                full_params['UserID'] = '710743'
                full_params['Token'] = '34e98bda3ee5c9a37af74eb66fb543a4'
                
                response = client.post('https://pchq.kaipanla.com/w1/api/index.php', data=full_params)
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"   ✅ 成功! 数据键: {list(data.keys()) if isinstance(data, dict) else type(data)}")
                        if isinstance(data, dict):
                            for key, value in data.items():
                                if isinstance(value, list):
                                    print(f"      {key}: {len(value)} 条记录")
                                    if len(value) > 0:
                                        print(f"         示例: {value[0]}")
                                elif isinstance(value, dict) and 'list' in value:
                                    print(f"      {key}.list: {len(value['list'])} 条记录")
                                    if 'Count' in value:
                                        print(f"      {key}.Count: {value['Count']}")
                                    if len(value['list']) > 0:
                                        print(f"         示例: {value['list'][0]}")
                    except Exception as e:
                        response_text = response.text[:200]
                        print(f"   ⚠️ 响应不是JSON: {response_text}...")
                else:
                    print(f"   ❌ 失败，状态码: {response.status_code}")
            except Exception as e:
                print(f"   ❌ 异常: {e}")
            
            print()  # 空行分隔

    finally:
        client.close()

if __name__ == "__main__":
    explore_stock_list_api()
