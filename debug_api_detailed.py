#!/usr/bin/env python3
"""
详细调试API响应问题
"""
import requests
import json
from config import get_config

config = get_config()

def test_different_plate_params():
    """测试不同的板块数据参数组合"""
    print("=== 测试不同的板块数据参数 ===")
    
    base_url = "https://pchq.kaipanla.com/w1/api/index.php"
    base_params = {
        'UserID': config.API_PARAMS_TEMPLATE['UserID'],
        'Token': config.API_PARAMS_TEMPLATE['Token']
    }
    
    # 测试不同的参数组合
    param_combinations = [
        # 原始参数
        {'c': 'PCArrangeData', 'a': 'GetPlateList', 'Day': '20250630'},
        # 尝试不同的action
        {'act': 'getPlateList', 'day': '20250630'},
        {'act': 'GetPlateList', 'Day': '20250630'},
        # 尝试不同的controller
        {'c': 'PlateData', 'a': 'GetList', 'Day': '20250630'},
        {'c': 'Plate', 'a': 'GetPlateData', 'Day': '20250630'},
        # 尝试股票数据的参数格式
        {'StockID': 'ALL', 'Day': '20250630'},
        # 尝试不带日期
        {'c': 'PCArrangeData', 'a': 'GetPlateList'},
        {'act': 'getPlateList'},
    ]
    
    for i, params in enumerate(param_combinations):
        print(f"\n--- 测试组合 {i+1} ---")
        test_params = base_params.copy()
        test_params.update(params)
        print(f"参数: {test_params}")
        
        try:
            response = requests.get(base_url, params=test_params, timeout=10)
            print(f"状态码: {response.status_code}")
            print(f"URL: {response.url}")
            
            content = response.text.strip()
            print(f"响应长度: {len(content)}")
            
            if content:
                print(f"响应内容 (前200字符): {content[:200]}")
                try:
                    data = response.json()
                    print(f"JSON解析成功: {type(data)}")
                    if isinstance(data, dict):
                        print(f"JSON键: {list(data.keys())}")
                except:
                    print("JSON解析失败")
            else:
                print("响应为空")
                
        except Exception as e:
            print(f"请求失败: {e}")

def test_different_lhb_params():
    """测试不同的龙虎榜数据参数组合"""
    print("\n\n=== 测试不同的龙虎榜数据参数 ===")
    
    base_url = "https://pclhb.kaipanla.com/w1/api/index.php"
    base_params = {
        'UserID': config.API_PARAMS_TEMPLATE['UserID'],
        'Token': config.API_PARAMS_TEMPLATE['Token']
    }
    
    # 测试不同的参数组合
    param_combinations = [
        # 原始参数
        {'c': 'LongHuBang', 'a': 'GetLongHuBangData', 'Day': '20250630'},
        # 尝试不同的action
        {'act': 'getLongHuBang', 'day': '20250630'},
        {'act': 'GetLongHuBang', 'Day': '20250630'},
        # 尝试不同的controller
        {'c': 'PCArrangeData', 'a': 'GetLongHuBang', 'Day': '20250630'},
        {'c': 'LHB', 'a': 'GetData', 'Day': '20250630'},
        # 尝试不带日期
        {'c': 'LongHuBang', 'a': 'GetLongHuBangData'},
        {'act': 'getLongHuBang'},
    ]
    
    for i, params in enumerate(param_combinations):
        print(f"\n--- 测试组合 {i+1} ---")
        test_params = base_params.copy()
        test_params.update(params)
        print(f"参数: {test_params}")
        
        try:
            response = requests.get(base_url, params=test_params, timeout=10)
            print(f"状态码: {response.status_code}")
            print(f"URL: {response.url}")
            
            content = response.text.strip()
            print(f"响应长度: {len(content)}")
            
            if content:
                print(f"响应内容 (前200字符): {content[:200]}")
                try:
                    data = response.json()
                    print(f"JSON解析成功: {type(data)}")
                    if isinstance(data, dict):
                        print(f"JSON键: {list(data.keys())}")
                except:
                    print("JSON解析失败")
            else:
                print("响应为空")
                
        except Exception as e:
            print(f"请求失败: {e}")

def test_stock_api_structure():
    """分析股票API的响应结构，看是否包含板块信息"""
    print("\n\n=== 分析股票API响应结构 ===")
    
    base_url = "https://pchq.kaipanla.com/w1/api/index.php"
    params = {
        'UserID': config.API_PARAMS_TEMPLATE['UserID'],
        'Token': config.API_PARAMS_TEMPLATE['Token'],
        'StockID': '000001',
        'Day': '20250630'
    }
    
    try:
        response = requests.get(base_url, params=params, timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("股票API响应的主要键:")
            for key in data.keys():
                print(f"  - {key}: {type(data[key])}")
                
            # 检查是否有板块相关信息
            if 'stockplate' in data:
                print(f"\nstockplate 数据样例:")
                plates = data['stockplate'][:3] if len(data['stockplate']) > 3 else data['stockplate']
                for plate in plates:
                    print(f"  {plate}")
                    
            if 'careplate' in data:
                print(f"\ncareplate 数据样例:")
                plates = data['careplate'][:3] if len(data['careplate']) > 3 else data['careplate']
                for plate in plates:
                    print(f"  {plate}")
                    
    except Exception as e:
        print(f"分析失败: {e}")

def test_different_dates():
    """测试不同日期的数据可用性"""
    print("\n\n=== 测试不同日期的数据可用性 ===")
    
    dates = ['20250630', '20250629', '20250628', '20250627', '20250626']
    
    for date in dates:
        print(f"\n--- 测试日期: {date} ---")
        
        # 测试板块数据
        try:
            response = requests.get("https://pchq.kaipanla.com/w1/api/index.php", params={
                'UserID': config.API_PARAMS_TEMPLATE['UserID'],
                'Token': config.API_PARAMS_TEMPLATE['Token'],
                'c': 'PCArrangeData',
                'a': 'GetPlateList',
                'Day': date
            }, timeout=10)
            
            content = response.text.strip()
            print(f"板块数据 - 状态码: {response.status_code}, 响应长度: {len(content)}")
            
        except Exception as e:
            print(f"板块数据测试失败: {e}")
            
        # 测试龙虎榜数据
        try:
            response = requests.get("https://pclhb.kaipanla.com/w1/api/index.php", params={
                'UserID': config.API_PARAMS_TEMPLATE['UserID'],
                'Token': config.API_PARAMS_TEMPLATE['Token'],
                'c': 'LongHuBang',
                'a': 'GetLongHuBangData',
                'Day': date
            }, timeout=10)
            
            content = response.text.strip()
            print(f"龙虎榜数据 - 状态码: {response.status_code}, 响应长度: {len(content)}")
            
        except Exception as e:
            print(f"龙虎榜数据测试失败: {e}")

if __name__ == "__main__":
    test_different_plate_params()
    test_different_lhb_params()
    test_stock_api_structure()
    test_different_dates()
