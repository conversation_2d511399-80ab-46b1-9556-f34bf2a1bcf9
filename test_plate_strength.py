#!/usr/bin/env python3
"""
测试板块强度数据收集
"""
from datetime import datetime
from core.data_collector import DataCollector

def test_plate_strength():
    """测试板块强度数据收集"""
    print("🧪 === 测试板块强度数据收集 ===\n")
    
    collector = DataCollector()
    try:
        # 获取今天日期
        today = datetime.now().strftime('%Y%m%d')
        
        print(f"📅 测试日期: {today}")
        
        # 测试板块强度数据收集
        print("💪 开始收集板块强度数据...")
        success = collector.collect_plate_strength_data(today)
        
        if success:
            print("✅ 板块强度数据收集成功！")
            
            # 查看收集到的数据
            print("\n📊 === 收集到的板块强度数据 ===")
            from simple_data_view import view_data
            view_data()
            
        else:
            print("❌ 板块强度数据收集失败")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        collector.close()

if __name__ == "__main__":
    test_plate_strength()
