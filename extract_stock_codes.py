#!/usr/bin/env python3
"""
提取股票代码
"""
from core.http_client import KaipanlaClient

def extract_stock_codes():
    """提取股票代码"""
    client = KaipanlaClient()
    
    try:
        print("🚀 获取5000只股票代码...")
        
        data = client.get_stock_ranking_list(start_index=0, count=5000)
        
        if data and isinstance(data, dict) and 'list' in data:
            stock_list = data['list']
            print(f"📊 获取到 {len(stock_list)} 只股票")
            
            stock_codes = []
            for item in stock_list:
                if isinstance(item, dict) and 'Code' in item:
                    code = str(item['Code'])
                    if code.isdigit() and len(code) == 6:
                        stock_codes.append(code)
            
            print(f"✅ 成功提取 {len(stock_codes)} 只有效股票代码")
            print(f"📝 前20只股票: {stock_codes[:20]}")
            
            # 保存到文件
            with open('stock_ranking_list.txt', 'w', encoding='utf-8') as f:
                for code in stock_codes:
                    f.write(f"{code}\n")
            print(f"💾 股票代码已保存到 stock_ranking_list.txt")
            
            return stock_codes
        else:
            print("❌ 获取失败或数据格式不正确")
            return []
            
    finally:
        client.close()

if __name__ == "__main__":
    codes = extract_stock_codes()
    print(f"\n🎉 总共获取到 {len(codes)} 只股票代码！")
