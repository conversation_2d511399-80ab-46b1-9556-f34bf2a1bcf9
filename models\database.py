"""
数据库模型定义
"""
from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Text, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
from config import get_config

config = get_config()
Base = declarative_base()

class StockInfo(Base):
    """股票基本信息表"""
    __tablename__ = 'stock_info'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(10), nullable=False, index=True, comment='股票代码')
    name = Column(String(50), nullable=False, comment='股票名称')
    market = Column(String(10), comment='市场(SH/SZ)')
    industry = Column(String(50), comment='所属行业')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')

class StockQuote(Base):
    """股票行情数据表"""
    __tablename__ = 'stock_quote'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(10), nullable=False, index=True, comment='股票代码')
    trade_date = Column(String(8), nullable=False, index=True, comment='交易日期')
    current_price = Column(Float, comment='当前价格')
    open_price = Column(Float, comment='开盘价')
    high_price = Column(Float, comment='最高价')
    low_price = Column(Float, comment='最低价')
    close_price = Column(Float, comment='收盘价')
    volume = Column(Float, comment='成交量')
    amount = Column(Float, comment='成交额')
    change_rate = Column(Float, comment='涨跌幅')
    change_amount = Column(Float, comment='涨跌额')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')

class StockTag(Base):
    """股票标签表"""
    __tablename__ = 'stock_tag'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(10), nullable=False, index=True, comment='股票代码')
    tag = Column(String(100), nullable=False, comment='标签内容')
    trade_date = Column(String(8), nullable=False, index=True, comment='交易日期')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')

class StockConcept(Base):
    """股票概念表"""
    __tablename__ = 'stock_concept'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(10), nullable=False, index=True, comment='股票代码')
    concept = Column(String(100), nullable=False, comment='概念名称')
    trade_date = Column(String(8), nullable=False, index=True, comment='交易日期')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')

class PlateInfo(Base):
    """板块信息表"""
    __tablename__ = 'plate_info'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    plate_code = Column(String(20), nullable=False, index=True, comment='板块代码')
    plate_name = Column(String(100), nullable=False, comment='板块名称')
    plate_type = Column(String(20), comment='板块类型')
    trade_date = Column(String(8), nullable=False, index=True, comment='交易日期')
    change_rate = Column(Float, comment='板块涨跌幅')
    stock_count = Column(Integer, comment='板块股票数量')
    up_count = Column(Integer, comment='上涨股票数')
    down_count = Column(Integer, comment='下跌股票数')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')

class LongHuBang(Base):
    """龙虎榜数据表"""
    __tablename__ = 'long_hu_bang'

    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(10), nullable=False, index=True, comment='股票代码')
    trade_date = Column(String(8), nullable=False, index=True, comment='交易日期')
    reason = Column(String(200), comment='上榜原因')
    buy_amount = Column(Float, comment='买入金额')
    sell_amount = Column(Float, comment='卖出金额')
    net_amount = Column(Float, comment='净买入金额')
    department = Column(String(200), comment='营业部')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')

class PlateStrength(Base):
    """板块强度数据表"""
    __tablename__ = 'plate_strength'

    id = Column(Integer, primary_key=True, autoincrement=True)
    plate_code = Column(String(20), nullable=False, index=True, comment='板块代码')
    plate_name = Column(String(100), nullable=False, comment='板块名称')
    strength = Column(Float, comment='板块强度')
    rise_speed = Column(Float, comment='涨速(%)')
    turnover = Column(Float, comment='成交额(元)')
    main_net_amount = Column(Float, comment='主力净额(元)')
    main_buy_amount = Column(Float, comment='主力买入(元)')
    main_sell_amount = Column(Float, comment='主力卖出(元)')
    volume_ratio = Column(Float, comment='量比')
    market_cap = Column(Float, comment='流通市值(元)')
    trade_date = Column(String(8), nullable=False, index=True, comment='交易日期')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')

# 数据库引擎和会话
def get_engine():
    """获取数据库引擎"""
    if config.DATABASE_URL.startswith('mysql'):
        return create_engine(config.get_mysql_url(), echo=False, pool_recycle=3600)
    else:
        return create_engine(config.DATABASE_URL, echo=False)

def get_session():
    """获取数据库会话"""
    engine = get_engine()
    Session = sessionmaker(bind=engine)
    return Session()

def init_database():
    """初始化数据库表"""
    engine = get_engine()
    Base.metadata.create_all(engine)
    print("数据库表创建完成")
