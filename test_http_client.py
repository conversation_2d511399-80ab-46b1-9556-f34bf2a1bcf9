#!/usr/bin/env python3
"""
测试HTTP客户端
"""
from core.http_client import KaipanlaClient
from config import get_config

config = get_config()

def test_http_client():
    """测试HTTP客户端的股票数据获取"""
    print("=== 测试HTTP客户端股票数据获取 ===")
    
    client = KaipanlaClient()
    try:
        data = client.get_stock_data('000001', '20250630')
        print(f'股票数据类型: {type(data)}')
        if data:
            print(f'股票数据键: {list(data.keys()) if isinstance(data, dict) else "非字典类型"}')
            print(f'数据内容: {str(data)[:200]}...')
        else:
            print('股票数据为空')
    finally:
        client.close()

if __name__ == "__main__":
    test_http_client()
