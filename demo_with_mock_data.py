#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
使用模拟数据演示系统功能
当API凭据不可用时，使用此脚本演示完整的数据采集流程
"""

import json
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any

from models.database import get_session, StockQuote, StockTag, StockConcept, PlateInfo, LongHuBang
from utils.logger import setup_logger

logger = setup_logger(__name__)

class MockDataGenerator:
    """模拟数据生成器"""
    
    def __init__(self):
        self.stock_codes = [
            '000001', '000002', '000858', '002415', '600000',
            '600036', '600519', '000858', '002594', '300059',
            '600276', '000725', '002304', '300015', '600887'
        ]
        
        self.stock_names = {
            '000001': '平安银行',
            '000002': '万科A',
            '000858': '五粮液',
            '002415': '海康威视',
            '600000': '浦发银行',
            '600036': '招商银行',
            '600519': '贵州茅台',
            '002594': '比亚迪',
            '300059': '东方财富',
            '600276': '恒瑞医药',
            '000725': '京东方A',
            '002304': '洋河股份',
            '300015': '爱尔眼科',
            '600887': '伊利股份'
        }
        
        self.concepts = [
            '新能源汽车', '人工智能', '5G概念', '芯片概念', '医药生物',
            '白酒概念', '银行股', '房地产', '新材料', '云计算',
            '物联网', '区块链', '军工概念', '环保概念', '消费电子'
        ]
        
        self.plates = [
            '沪深300', '上证50', '创业板', '科创板', '中小板',
            '银行板块', '地产板块', '医药板块', '科技板块', '消费板块'
        ]
    
    def generate_stock_quote(self, stock_code: str, trade_date: str) -> Dict[str, Any]:
        """生成股票行情数据"""
        base_price = random.uniform(10, 200)
        change_rate = random.uniform(-10, 10)
        
        current_price = base_price * (1 + change_rate / 100)
        open_price = base_price * random.uniform(0.98, 1.02)
        high_price = max(current_price, open_price) * random.uniform(1.0, 1.05)
        low_price = min(current_price, open_price) * random.uniform(0.95, 1.0)
        close_price = current_price
        
        volume = random.randint(1000000, 100000000)
        amount = volume * current_price
        
        return {
            'symbol': stock_code,
            'current_price': round(current_price, 2),
            'open_price': round(open_price, 2),
            'high_price': round(high_price, 2),
            'low_price': round(low_price, 2),
            'close_price': round(close_price, 2),
            'volume': volume,
            'amount': round(amount, 2),
            'change_rate': round(change_rate, 2),
            'change_amount': round(current_price - base_price, 2),
            'trade_date': trade_date
        }
    
    def generate_stock_tags(self, stock_code: str, trade_date: str) -> List[str]:
        """生成股票标签"""
        tags = []
        if random.random() > 0.7:  # 30%概率有标签
            num_tags = random.randint(1, 3)
            tags = random.sample(self.concepts, min(num_tags, len(self.concepts)))
        return tags
    
    def generate_stock_concepts(self, stock_code: str, trade_date: str) -> List[str]:
        """生成股票概念"""
        num_concepts = random.randint(2, 5)
        return random.sample(self.concepts, min(num_concepts, len(self.concepts)))
    
    def generate_plate_info(self, trade_date: str) -> List[Dict[str, Any]]:
        """生成板块信息"""
        plate_data = []
        for i, plate in enumerate(self.plates):
            change_rate = random.uniform(-5, 5)
            plate_data.append({
                'plate_code': f'PL{i+1:03d}',  # 生成板块代码
                'plate_name': plate,
                'plate_type': '指数' if '指数' in plate or '板块' in plate else '概念',
                'change_rate': round(change_rate, 2),
                'stock_count': random.randint(10, 100),
                'up_count': random.randint(5, 50),
                'down_count': random.randint(5, 50),
                'trade_date': trade_date
            })
        return plate_data
    
    def generate_longhubang_data(self, trade_date: str) -> List[Dict[str, Any]]:
        """生成龙虎榜数据"""
        lhb_data = []
        selected_stocks = random.sample(self.stock_codes, random.randint(3, 8))
        
        for stock_code in selected_stocks:
            buy_amount = round(random.uniform(10000000, 100000000), 2)
            sell_amount = round(random.uniform(10000000, 100000000), 2)
            lhb_data.append({
                'symbol': stock_code,
                'net_amount': round(buy_amount - sell_amount, 2),
                'buy_amount': buy_amount,
                'sell_amount': sell_amount,
                'reason': random.choice(['涨幅偏离值达7%', '跌幅偏离值达7%', '换手率达20%', '连续三个交易日内涨幅偏离值累计达20%']),
                'department': random.choice(['华泰证券北京分公司', '中信证券上海分公司', '国泰君安深圳分公司', '招商证券广州分公司']),
                'trade_date': trade_date
            })
        return lhb_data

def save_mock_data_to_database(trade_date: str):
    """将模拟数据保存到数据库"""
    logger.info(f"开始生成并保存模拟数据，日期: {trade_date}")
    
    generator = MockDataGenerator()
    session = get_session()
    
    try:
        # 生成并保存股票行情数据
        logger.info("生成股票行情数据...")
        for stock_code in generator.stock_codes:
            quote_data = generator.generate_stock_quote(stock_code, trade_date)
            
            # 检查是否已存在
            existing = session.query(StockQuote).filter_by(
                symbol=stock_code, trade_date=trade_date
            ).first()
            
            if not existing:
                quote = StockQuote(**quote_data)
                session.add(quote)
            
            # 生成标签数据
            tags = generator.generate_stock_tags(stock_code, trade_date)
            for tag in tags:
                existing_tag = session.query(StockTag).filter_by(
                    symbol=stock_code, trade_date=trade_date, tag=tag
                ).first()
                
                if not existing_tag:
                    stock_tag = StockTag(
                        symbol=stock_code,
                        tag=tag,
                        trade_date=trade_date
                    )
                    session.add(stock_tag)
            
            # 生成概念数据
            concepts = generator.generate_stock_concepts(stock_code, trade_date)
            for concept in concepts:
                existing_concept = session.query(StockConcept).filter_by(
                    symbol=stock_code, trade_date=trade_date, concept=concept
                ).first()
                
                if not existing_concept:
                    stock_concept = StockConcept(
                        symbol=stock_code,
                        concept=concept,
                        trade_date=trade_date
                    )
                    session.add(stock_concept)
        
        # 生成并保存板块数据
        logger.info("生成板块数据...")
        plate_data_list = generator.generate_plate_info(trade_date)
        for plate_data in plate_data_list:
            existing_plate = session.query(PlateInfo).filter_by(
                plate_name=plate_data['plate_name'], trade_date=trade_date
            ).first()
            
            if not existing_plate:
                plate = PlateInfo(**plate_data)
                session.add(plate)
        
        # 生成并保存龙虎榜数据
        logger.info("生成龙虎榜数据...")
        lhb_data_list = generator.generate_longhubang_data(trade_date)
        for lhb_data in lhb_data_list:
            existing_lhb = session.query(LongHuBang).filter_by(
                symbol=lhb_data['symbol'], trade_date=trade_date
            ).first()
            
            if not existing_lhb:
                lhb = LongHuBang(**lhb_data)
                session.add(lhb)
        
        # 提交所有更改
        session.commit()
        logger.info("模拟数据保存成功")
        
        # 统计保存的数据
        quote_count = session.query(StockQuote).filter_by(trade_date=trade_date).count()
        tag_count = session.query(StockTag).filter_by(trade_date=trade_date).count()
        concept_count = session.query(StockConcept).filter_by(trade_date=trade_date).count()
        plate_count = session.query(PlateInfo).filter_by(trade_date=trade_date).count()
        lhb_count = session.query(LongHuBang).filter_by(trade_date=trade_date).count()
        
        print(f"✅ 模拟数据生成完成！")
        print(f"   股票行情: {quote_count} 条")
        print(f"   股票标签: {tag_count} 条")
        print(f"   股票概念: {concept_count} 条")
        print(f"   板块信息: {plate_count} 条")
        print(f"   龙虎榜: {lhb_count} 条")
        
        return True
        
    except Exception as e:
        session.rollback()
        logger.error(f"保存模拟数据失败: {str(e)}")
        print(f"❌ 保存模拟数据失败: {str(e)}")
        return False
    finally:
        session.close()

def demo_system_with_mock_data():
    """使用模拟数据演示系统功能"""
    print("🎭 开盘啦数据采集系统 - 模拟数据演示")
    print("=" * 50)
    
    # 生成今天的模拟数据
    today = datetime.now().strftime('%Y%m%d')
    
    print(f"正在为 {today} 生成模拟数据...")
    if save_mock_data_to_database(today):
        print("\n🎉 模拟数据生成成功！")
        print("\n现在您可以测试以下功能：")
        print("  python main.py status     # 查看数据统计")
        print("  python main.py export     # 导出数据到Excel")
        print("  python test_spider.py basic  # 运行基础测试")
        
        print("\n📊 系统演示完成！")
        print("当您获得有效的API凭据后，系统将能够采集真实的股票数据。")
    else:
        print("❌ 模拟数据生成失败")

if __name__ == "__main__":
    demo_system_with_mock_data()
