#!/usr/bin/env python3
"""
调试API响应编码问题
"""
import requests
from config import get_config

config = get_config()

def analyze_stock_api_response():
    """分析股票API响应的编码和格式问题"""
    print("=== 股票API原始响应分析 ===")
    
    url = 'https://pchq.kaipanla.com/w1/api/index.php'
    params = {
        'UserID': config.API_PARAMS_TEMPLATE['UserID'],
        'Token': config.API_PARAMS_TEMPLATE['Token'],
        'StockID': '000001',
        'Day': '20250630'
    }
    
    response = requests.get(url, params=params, timeout=10)
    print(f'状态码: {response.status_code}')
    print(f'响应头: {dict(response.headers)}')
    print(f'响应长度: {len(response.text)}')
    print(f'响应内容类型: {response.headers.get("Content-Type")}')
    
    # 检查响应的原始字节
    raw_content = response.content
    print(f'原始字节长度: {len(raw_content)}')
    print(f'前100字节: {raw_content[:100]}')
    
    # 检查是否有BOM或特殊字符
    text_content = response.text
    print(f'文本内容长度: {len(text_content)}')
    print(f'前200字符: {repr(text_content[:200])}')
    
    # 尝试不同的编码
    try:
        utf8_content = raw_content.decode('utf-8')
        print(f'UTF-8解码成功，长度: {len(utf8_content)}')
        print(f'UTF-8前200字符: {repr(utf8_content[:200])}')
    except Exception as e:
        print(f'UTF-8解码失败: {e}')
    
    try:
        gbk_content = raw_content.decode('gbk')
        print(f'GBK解码成功，长度: {len(gbk_content)}')
        print(f'GBK前200字符: {repr(gbk_content[:200])}')
    except Exception as e:
        print(f'GBK解码失败: {e}')
    
    # 尝试清理和解析JSON
    try:
        # 移除可能的BOM
        clean_text = text_content.lstrip('\ufeff')
        print(f'移除BOM后长度: {len(clean_text)}')
        
        # 尝试解析JSON
        import json
        data = json.loads(clean_text)
        print(f'JSON解析成功: {type(data)}')
        if isinstance(data, dict):
            print(f'JSON主要键: {list(data.keys())}')
    except Exception as e:
        print(f'JSON解析失败: {e}')
        
        # 尝试查找JSON开始位置
        for i, char in enumerate(text_content):
            if char in ['{', '[']:
                print(f'找到JSON开始位置: {i}, 字符: {char}')
                try:
                    json_part = text_content[i:]
                    data = json.loads(json_part)
                    print(f'从位置{i}开始的JSON解析成功')
                    if isinstance(data, dict):
                        print(f'JSON主要键: {list(data.keys())}')
                    break
                except:
                    continue

def test_plate_api_with_different_methods():
    """测试板块API是否需要POST方法"""
    print("\n\n=== 测试板块API的不同请求方法 ===")
    
    url = 'https://pchq.kaipanla.com/w1/api/index.php'
    params = {
        'UserID': config.API_PARAMS_TEMPLATE['UserID'],
        'Token': config.API_PARAMS_TEMPLATE['Token'],
        'c': 'PCArrangeData',
        'a': 'GetPlateList',
        'Day': '20250630'
    }
    
    # 测试GET方法
    print("--- GET方法 ---")
    try:
        response = requests.get(url, params=params, timeout=10)
        print(f'GET - 状态码: {response.status_code}, 响应长度: {len(response.text)}')
        if response.text.strip():
            print(f'GET - 响应内容: {response.text[:100]}')
    except Exception as e:
        print(f'GET请求失败: {e}')
    
    # 测试POST方法
    print("--- POST方法 ---")
    try:
        response = requests.post(url, data=params, timeout=10)
        print(f'POST - 状态码: {response.status_code}, 响应长度: {len(response.text)}')
        if response.text.strip():
            print(f'POST - 响应内容: {response.text[:100]}')
    except Exception as e:
        print(f'POST请求失败: {e}')
    
    # 测试POST JSON方法
    print("--- POST JSON方法 ---")
    try:
        import json
        response = requests.post(url, json=params, timeout=10)
        print(f'POST JSON - 状态码: {response.status_code}, 响应长度: {len(response.text)}')
        if response.text.strip():
            print(f'POST JSON - 响应内容: {response.text[:100]}')
    except Exception as e:
        print(f'POST JSON请求失败: {e}')

def check_api_endpoints():
    """检查不同的API端点是否存在"""
    print("\n\n=== 检查API端点可用性 ===")
    
    endpoints = [
        'https://pchq.kaipanla.com/w1/api/index.php',
        'https://pclhb.kaipanla.com/w1/api/index.php',
        'https://pchq.kaipanla.com/api/index.php',
        'https://pclhb.kaipanla.com/api/index.php',
        'https://api.kaipanla.com/w1/api/index.php',
        'https://www.kaipanla.com/w1/api/index.php'
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(endpoint, timeout=5)
            print(f'{endpoint} - 状态码: {response.status_code}')
        except Exception as e:
            print(f'{endpoint} - 请求失败: {e}')

if __name__ == "__main__":
    analyze_stock_api_response()
    test_plate_api_with_different_methods()
    check_api_endpoints()
