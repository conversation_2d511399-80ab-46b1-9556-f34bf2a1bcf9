#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
开盘啦登录助手
帮助用户获取和更新API凭据
"""

import sys
import json
from core.login_manager import KaipanlaLoginManager
from utils.logger import setup_logger

logger = setup_logger(__name__)

def show_manual_instructions():
    """显示手动获取凭据的详细说明"""
    instructions = """
    ==========================================
    开盘啦API凭据获取指南
    ==========================================
    
    由于开盘啦网站可能有反爬虫机制，推荐使用以下手动方法获取凭据：
    
    方法一：浏览器开发者工具（推荐）
    ----------------------------------------
    1. 打开Chrome或Firefox浏览器
    2. 访问 https://www.kaipanla.com
    3. 登录您的账号
    4. 按F12打开开发者工具
    5. 切换到 "Network" (网络) 标签
    6. 清空网络记录（点击清除按钮）
    7. 在网站上查看任意股票数据
    8. 在网络请求中找到发送到以下URL的请求：
       - https://pchq.kaipanla.com/w1/api/index.php
       - https://pclhb.kaipanla.com/w1/api/index.php
    9. 点击该请求，查看 "Request Payload" 或 "Form Data"
    10. 找到 UserID 和 Token 参数
    
    方法二：浏览器Cookie（备选）
    ----------------------------------------
    1. 登录开盘啦网站后
    2. 在开发者工具的 "Application" 标签中
    3. 查看 "Cookies" 部分
    4. 寻找包含用户信息的cookie值
    
    示例参数格式：
    ----------------------------------------
    UserID: 123456 (数字)
    Token: abcdef1234567890abcdef1234567890 (32位字符串)
    
    获取到凭据后，请运行：
    python login_helper.py --update-config --user-id YOUR_USER_ID --token YOUR_TOKEN
    """
    
    print(instructions)

def test_current_credentials():
    """测试当前配置的凭据"""
    print("正在测试当前配置的凭据...")
    
    login_manager = KaipanlaLoginManager()
    
    try:
        from config import get_config
        config = get_config()
        
        user_id = config.API_PARAMS_TEMPLATE.get('710743')
        token = config.API_PARAMS_TEMPLATE.get('34e98bda3ee5c9a37af74eb66fb543a4')

        print(f"当前UserID: {user_id}")
        print(f"当前Token: {token[:8]}..." if token else "无Token")
        
        if login_manager.test_credentials(user_id, token):
            print("✅ 当前凭据有效！")
            return True
        else:
            print("❌ 当前凭据已失效")
            return False
            
    except Exception as e:
        print(f"❌ 测试凭据时发生错误: {str(e)}")
        return False
    finally:
        login_manager.close()

def update_credentials(user_id: int, token: str):
    """更新配置文件中的凭据"""
    print(f"正在更新凭据...")
    print(f"UserID: {user_id}")
    print(f"Token: {token[:8]}...")
    
    login_manager = KaipanlaLoginManager()
    
    try:
        # 先测试凭据是否有效
        if not login_manager.test_credentials(user_id, token):
            print("❌ 提供的凭据无效，请检查后重试")
            return False
        
        # 更新配置文件
        if login_manager.update_config_credentials(user_id, token):
            print("✅ 凭据更新成功！")
            
            # 再次测试确认
            print("正在验证更新后的配置...")
            if test_current_credentials():
                print("🎉 配置更新完成，系统已就绪！")
                return True
            else:
                print("❌ 配置更新后验证失败")
                return False
        else:
            print("❌ 更新配置文件失败")
            return False
            
    except Exception as e:
        print(f"❌ 更新凭据时发生错误: {str(e)}")
        return False
    finally:
        login_manager.close()

def try_auto_login(username: str, password: str):
    """尝试自动登录（实验性功能）"""
    print("⚠️  正在尝试自动登录（实验性功能）...")
    print("注意：此功能可能因网站反爬虫机制而失败")
    
    login_manager = KaipanlaLoginManager()
    
    try:
        if login_manager.simulate_login(username, password):
            print("✅ 自动登录成功！")
            
            if login_manager.user_id and login_manager.token:
                print(f"获取到凭据:")
                print(f"UserID: {login_manager.user_id}")
                print(f"Token: {login_manager.token[:8]}...")
                
                # 询问是否更新配置
                response = input("是否将这些凭据保存到配置文件？(y/n): ")
                if response.lower() in ['y', 'yes', '是']:
                    return update_credentials(login_manager.user_id, login_manager.token)
            else:
                print("❌ 登录成功但未能提取到凭据")
                print("请使用手动方法获取凭据")
                return False
        else:
            print("❌ 自动登录失败")
            print("请使用手动方法获取凭据")
            return False
            
    except Exception as e:
        print(f"❌ 自动登录过程中发生错误: {str(e)}")
        return False
    finally:
        login_manager.close()

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='开盘啦登录助手')
    parser.add_argument('--test', action='store_true', help='测试当前凭据')
    parser.add_argument('--instructions', action='store_true', help='显示获取凭据的详细说明')
    parser.add_argument('--update-config', action='store_true', help='更新配置文件中的凭据')
    parser.add_argument('--user-id', type=int, help='用户ID')
    parser.add_argument('--token', type=str, help='用户Token')
    parser.add_argument('--auto-login', action='store_true', help='尝试自动登录（实验性）')
    parser.add_argument('--username', type=str, help='用户名（用于自动登录）')
    parser.add_argument('--password', type=str, help='密码（用于自动登录）')
    
    args = parser.parse_args()
    
    if args.instructions:
        show_manual_instructions()
    elif args.test:
        test_current_credentials()
    elif args.update_config:
        if not args.user_id or not args.token:
            print("❌ 请提供 --user-id 和 --token 参数")
            sys.exit(1)
        update_credentials(args.user_id, args.token)
    elif args.auto_login:
        if not args.username or not args.password:
            print("❌ 请提供 --username 和 --password 参数")
            sys.exit(1)
        try_auto_login(args.username, args.password)
    else:
        # 默认行为：显示当前状态和选项
        print("开盘啦登录助手")
        print("=" * 40)
        
        # 测试当前凭据
        if test_current_credentials():
            print("\n🎉 当前凭据有效，系统已就绪！")
            print("\n可用命令：")
            print("  python main.py collect    # 开始数据采集")
            print("  python main.py status     # 查看系统状态")
        else:
            print("\n❌ 需要更新凭据")
            print("\n可用选项：")
            print("  python login_helper.py --instructions     # 查看获取凭据的详细说明")
            print("  python login_helper.py --test            # 测试当前凭据")
            print("  python login_helper.py --update-config --user-id YOUR_ID --token YOUR_TOKEN")
            print("  python login_helper.py --auto-login --username USER --password PASS  # 实验性自动登录")

if __name__ == "__main__":
    main()
