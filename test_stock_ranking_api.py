#!/usr/bin/env python3
"""
测试股票排行榜API
"""
from core.http_client import KaipanlaClient
import json

def test_stock_ranking_api():
    """测试股票排行榜API"""
    client = KaipanlaClient()
    
    try:
        print("🚀 === 测试股票排行榜API ===\n")
        
        # 测试小批量数据
        print("1️⃣ 测试获取前50只股票...")
        data = client.get_stock_ranking_list(start_index=0, count=50)
        
        if data:
            print(f"✅ 获取成功，数据类型: {type(data)}")
            
            # 分析数据结构
            if isinstance(data, dict):
                print(f"🔑 数据键: {list(data.keys())}")
                
                # 查找股票列表数据
                for key, value in data.items():
                    if isinstance(value, list) and len(value) > 0:
                        print(f"📋 发现列表数据 '{key}': 长度 {len(value)}")
                        if len(value) > 0:
                            print(f"🔍 第一个元素: {json.dumps(value[0], ensure_ascii=False, indent=2)}")
                        break
                        
            print(f"📊 完整数据结构: {json.dumps(data, ensure_ascii=False, indent=2)[:1000]}...")
            
        else:
            print("❌ 获取失败")
            
        print("\n" + "="*50 + "\n")
        
        # 测试更大批量数据
        print("2️⃣ 测试获取前500只股票...")
        data_large = client.get_stock_ranking_list(start_index=0, count=500)
        
        if data_large:
            print(f"✅ 大批量获取成功")
            
            # 分析数据结构
            if isinstance(data_large, dict):
                for key, value in data_large.items():
                    if isinstance(value, list):
                        print(f"📋 列表数据 '{key}': 长度 {len(value)}")
                        
        else:
            print("❌ 大批量获取失败")
            
        print("\n" + "="*50 + "\n")
        
        # 测试超大批量数据
        print("3️⃣ 测试获取前5000只股票...")
        data_huge = client.get_stock_ranking_list(start_index=0, count=5000)
        
        if data_huge:
            print(f"✅ 超大批量获取成功")
            
            # 分析数据结构并提取股票代码
            stock_codes = []
            if isinstance(data_huge, dict):
                for key, value in data_huge.items():
                    if isinstance(value, list):
                        print(f"📋 列表数据 '{key}': 长度 {len(value)}")
                        
                        # 尝试提取股票代码
                        if len(value) > 0 and key == 'list':
                            first_item = value[0]
                            if isinstance(first_item, dict) and 'Code' in first_item:
                                print(f"🎯 在 'list' 中找到股票代码字段 'Code': {first_item['Code']}")
                                # 提取所有股票代码
                                for item in value:
                                    if isinstance(item, dict) and 'Code' in item:
                                        code = str(item['Code'])
                                        if code.isdigit() and len(code) == 6:
                                            stock_codes.append(code)
                                print(f"📊 成功提取 {len(stock_codes)} 只股票代码")
                                break
                                        
            if stock_codes:
                print(f"🎉 成功提取到 {len(stock_codes)} 只股票代码!")
                print(f"📝 前10只股票: {stock_codes[:10]}")
                
                # 保存到文件
                with open('stock_ranking_list.txt', 'w', encoding='utf-8') as f:
                    for code in stock_codes:
                        f.write(f"{code}\n")
                print(f"💾 股票代码已保存到 stock_ranking_list.txt")
            else:
                print("❌ 未能提取到股票代码")
                
        else:
            print("❌ 超大批量获取失败")
            
    finally:
        client.close()

if __name__ == "__main__":
    test_stock_ranking_api()
