#!/usr/bin/env python3
"""
监控数据采集进度
"""
import time
import sqlite3
from datetime import datetime

def monitor_progress():
    """监控数据采集进度"""
    print("🔍 === 数据采集进度监控 ===\n")
    
    start_time = time.time()
    last_count = 0
    
    while True:
        try:
            # 连接数据库
            conn = sqlite3.connect('kaipanla_data.db')
            cursor = conn.cursor()
            
            # 查询今日股票数据数量
            today = datetime.now().strftime('%Y%m%d')
            cursor.execute("SELECT COUNT(*) FROM stock_quotes WHERE trade_date = ?", (today,))
            current_count = cursor.fetchone()[0]
            
            # 查询总股票数据数量
            cursor.execute("SELECT COUNT(*) FROM stock_quotes")
            total_count = cursor.fetchone()[0]
            
            # 计算进度
            elapsed_time = time.time() - start_time
            speed = (current_count - last_count) / 60 if elapsed_time > 60 else 0  # 每分钟采集速度
            
            # 估算剩余时间
            if speed > 0 and current_count < 5000:
                remaining = (5000 - current_count) / speed
                eta = f"{remaining:.1f}分钟"
            else:
                eta = "计算中..."
            
            print(f"\r📊 今日采集: {current_count}/5000 ({current_count/50:.1f}%) | "
                  f"总数据: {total_count} | "
                  f"速度: {speed:.1f}/分钟 | "
                  f"预计剩余: {eta}", end="", flush=True)
            
            conn.close()
            
            # 如果采集完成，退出监控
            if current_count >= 5000:
                print(f"\n🎉 采集完成！总共采集了 {current_count} 只股票数据")
                break
                
            last_count = current_count
            time.sleep(10)  # 每10秒检查一次
            
        except KeyboardInterrupt:
            print("\n\n⏹️ 监控已停止")
            break
        except Exception as e:
            print(f"\n❌ 监控错误: {e}")
            time.sleep(5)

if __name__ == "__main__":
    monitor_progress()
