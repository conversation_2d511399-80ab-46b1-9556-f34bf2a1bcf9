#!/usr/bin/env python3
"""
测试所有修复后的API
"""
from core.http_client import KaipanlaClient

def test_all_apis():
    """测试所有API"""
    print("🚀 === 测试所有修复后的API ===")
    
    client = KaipanlaClient()
    try:
        # 1. 测试股票数据API
        print("\n1️⃣ === 测试股票数据API ===")
        stock_data = client.get_stock_data('000001', '20250630')
        if stock_data:
            print("✅ 股票API成功")
            print(f"   数据键: {list(stock_data.keys())}")
        else:
            print("❌ 股票API失败")
        
        # 2. 测试板块数据API（带日期）
        print("\n2️⃣ === 测试板块数据API（带日期）===")
        plate_data = client.get_plate_data('2025-06-30')
        if plate_data:
            print("✅ 板块API（带日期）成功")
            print(f"   数据键: {list(plate_data.keys())}")
            if 'list' in plate_data:
                print(f"   板块数量: {len(plate_data['list'])}")
        else:
            print("❌ 板块API（带日期）失败")
        
        # 3. 测试核心板块数据API（不带日期）
        print("\n3️⃣ === 测试核心板块数据API（不带日期）===")
        core_plate_data = client.get_core_plate_data()
        if core_plate_data:
            print("✅ 核心板块API成功")
            print(f"   数据键: {list(core_plate_data.keys())}")
            if 'list' in core_plate_data:
                print(f"   核心板块数量: {len(core_plate_data['list'])}")
                if len(core_plate_data['list']) > 0:
                    print(f"   第一个板块: {core_plate_data['list'][0]}")
        else:
            print("❌ 核心板块API失败")
        
        # 4. 测试龙虎榜数据API
        print("\n4️⃣ === 测试龙虎榜数据API ===")
        lhb_data = client.get_longhubang_data('20250630')
        if lhb_data:
            print("✅ 龙虎榜API成功")
            print(f"   数据键: {list(lhb_data.keys())}")
            if 'list' in lhb_data:
                print(f"   龙虎榜股票数量: {len(lhb_data['list'])}")
        else:
            print("❌ 龙虎榜API失败")
        
        # 5. 测试新闻数据API
        print("\n5️⃣ === 测试新闻数据API ===")
        news_data = client.get_news_data()
        if news_data:
            print("✅ 新闻API成功")
            print(f"   数据键: {list(news_data.keys())}")
            if 'list' in news_data:
                print(f"   新闻数量: {len(news_data['list'])}")
        else:
            print("❌ 新闻API失败")
        
        # 总结
        print("\n📊 === API测试总结 ===")
        success_count = sum([
            1 if stock_data else 0,
            1 if plate_data else 0,
            1 if core_plate_data else 0,
            1 if lhb_data else 0,
            1 if news_data else 0
        ])
        print(f"成功: {success_count}/5 个API")
        
        if success_count == 5:
            print("🎉 所有API都修复成功！")
        elif success_count >= 3:
            print("👍 大部分API修复成功！")
        else:
            print("⚠️  还有API需要进一步调试")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
    finally:
        client.close()

if __name__ == "__main__":
    test_all_apis()
