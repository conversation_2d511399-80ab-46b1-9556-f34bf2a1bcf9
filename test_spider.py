#!/usr/bin/env python3
"""
开盘啦数据采集项目测试脚本
"""
import unittest
import tempfile
import os
from datetime import datetime

from core.http_client import KaipanlaClient
from core.data_collector import DataCollector
from core.data_processor import DataProcessor
from models.database import init_database, get_session
from utils.logger import setup_logger
from config import get_config

logger = setup_logger(__name__)

class TestKaipanlaSpider(unittest.TestCase):
    """测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        # 使用临时数据库进行测试
        cls.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        cls.temp_db.close()
        
        # 修改配置使用临时数据库
        config = get_config()
        config.DATABASE_URL = f'sqlite:///{cls.temp_db.name}'
        
        # 初始化测试数据库
        init_database()
        logger.info(f"测试数据库创建: {cls.temp_db.name}")
    
    @classmethod
    def tearDownClass(cls):
        """测试类清理"""
        # 删除临时数据库文件
        if os.path.exists(cls.temp_db.name):
            os.unlink(cls.temp_db.name)
            logger.info("测试数据库已删除")
    
    def test_http_client(self):
        """测试HTTP客户端"""
        logger.info("测试HTTP客户端...")
        
        client = KaipanlaClient()
        
        # 测试连接
        try:
            success = client.test_connection()
            logger.info(f"连接测试结果: {success}")
            # 注意：由于可能没有有效的token，连接可能失败，这是正常的
        except Exception as e:
            logger.warning(f"连接测试异常: {str(e)}")
        
        client.close()
        logger.info("HTTP客户端测试完成")
    
    def test_data_collector(self):
        """测试数据采集器"""
        logger.info("测试数据采集器...")
        
        with DataCollector() as collector:
            # 测试获取股票列表
            stock_list = collector.get_stock_list_from_tushare()
            self.assertIsInstance(stock_list, list)
            logger.info(f"获取股票列表: {len(stock_list)} 只")
            
            # 测试采集单只股票数据（使用模拟数据）
            trade_date = datetime.now().strftime('%Y%m%d')
            if stock_list:
                test_stock = stock_list[0]
                try:
                    success = collector.collect_stock_data(test_stock, trade_date)
                    logger.info(f"采集股票 {test_stock} 数据: {'成功' if success else '失败'}")
                except Exception as e:
                    logger.warning(f"采集股票数据异常: {str(e)}")
        
        logger.info("数据采集器测试完成")
    
    def test_data_processor(self):
        """测试数据处理器"""
        logger.info("测试数据处理器...")
        
        with DataProcessor() as processor:
            # 测试数据统计
            stats = processor.get_data_statistics()
            self.assertIsInstance(stats, dict)
            logger.info(f"数据统计: {stats}")
            
            # 测试数据清洗
            trade_date = datetime.now().strftime('%Y%m%d')
            try:
                clean_results = processor.clean_stock_data(trade_date)
                logger.info(f"数据清洗结果: {clean_results}")
            except Exception as e:
                logger.warning(f"数据清洗异常: {str(e)}")
        
        logger.info("数据处理器测试完成")
    
    def test_database_operations(self):
        """测试数据库操作"""
        logger.info("测试数据库操作...")
        
        session = get_session()
        
        try:
            # 测试数据库连接
            from sqlalchemy import text
            result = session.execute(text("SELECT 1")).fetchone()
            self.assertEqual(result[0], 1)
            logger.info("数据库连接正常")
            
            # 测试表是否存在
            tables = ['stock_info', 'stock_quote', 'stock_tag', 'stock_concept', 'plate_info', 'long_hu_bang']
            for table in tables:
                try:
                    session.execute(f"SELECT COUNT(*) FROM {table}").fetchone()
                    logger.info(f"表 {table} 存在")
                except Exception as e:
                    logger.error(f"表 {table} 不存在或有问题: {str(e)}")
            
        finally:
            session.close()
        
        logger.info("数据库操作测试完成")
    
    def test_config(self):
        """测试配置"""
        logger.info("测试配置...")
        
        config = get_config()
        
        # 测试基本配置
        self.assertIsNotNone(config.KAIPANLA_API_BASE)
        self.assertIsNotNone(config.DEFAULT_HEADERS)
        self.assertIsInstance(config.REQUEST_TIMEOUT, int)
        
        logger.info("配置测试完成")
    
    def test_error_handling(self):
        """测试错误处理"""
        logger.info("测试错误处理...")
        
        from utils.error_handler import retry_on_exception, global_error_collector
        
        # 测试重试装饰器
        @retry_on_exception(max_attempts=2, delay=0.1)
        def test_function():
            raise Exception("测试异常")
        
        with self.assertRaises(Exception):
            test_function()
        
        # 测试错误收集器
        global_error_collector.clear()
        global_error_collector.add_error("测试错误")
        global_error_collector.add_warning("测试警告")
        
        summary = global_error_collector.get_summary()
        self.assertEqual(summary['error_count'], 1)
        self.assertEqual(summary['warning_count'], 1)
        
        logger.info("错误处理测试完成")

def run_basic_test():
    """运行基础测试"""
    logger.info("=== 开始基础功能测试 ===")
    
    # 测试API连接
    logger.info("1. 测试API连接...")
    try:
        client = KaipanlaClient()
        success = client.test_connection()
        logger.info(f"   API连接: {'✅ 成功' if success else '❌ 失败'}")
        client.close()
    except Exception as e:
        logger.error(f"   API连接异常: {str(e)}")
    
    # 测试数据库
    logger.info("2. 测试数据库...")
    try:
        init_database()
        session = get_session()
        from sqlalchemy import text
        result = session.execute(text("SELECT 1")).fetchone()
        session.close()
        logger.info(f"   数据库连接: {'✅ 成功' if result else '❌ 失败'}")
    except Exception as e:
        logger.error(f"   数据库连接异常: {str(e)}")
    
    # 测试数据采集
    logger.info("3. 测试数据采集...")
    try:
        with DataCollector() as collector:
            stock_list = collector.get_stock_list_from_tushare()
            logger.info(f"   获取股票列表: ✅ 成功 ({len(stock_list)} 只)")
    except Exception as e:
        logger.error(f"   数据采集异常: {str(e)}")
    
    logger.info("=== 基础功能测试完成 ===")

def run_full_test():
    """运行完整测试"""
    logger.info("=== 开始完整测试 ===")
    
    # 运行单元测试
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    logger.info("=== 完整测试完成 ===")

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'basic':
        run_basic_test()
    else:
        run_full_test()
