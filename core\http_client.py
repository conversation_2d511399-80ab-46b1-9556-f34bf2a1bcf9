"""
HTTP客户端模块
处理所有HTTP请求，包括会话管理、重试机制等
"""
import requests
import time
import json
from typing import Dict, Any, Optional
from retrying import retry
from fake_useragent import UserAgent
from config import get_config
from utils.logger import setup_logger, log_api_request

config = get_config()
logger = setup_logger(__name__)

class KaipanlaClient:
    """开盘啦API客户端"""
    
    def __init__(self):
        self.session = requests.Session()
        self.ua = UserAgent()
        self._setup_session()
        
    def _setup_session(self):
        """设置会话配置"""
        # 设置默认请求头
        self.session.headers.update(config.DEFAULT_HEADERS)

        # 添加开盘啦网站特定的请求头
        self.session.headers.update({
            'Origin': 'https://www.kaipanla.com',
            'Referer': 'https://www.kaipanla.com/',
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
        })

        # 设置超时
        self.session.timeout = config.REQUEST_TIMEOUT
        
        # 禁用SSL验证警告
        requests.packages.urllib3.disable_warnings()
        
    def _get_random_user_agent(self):
        """获取随机User-Agent"""
        try:
            return self.ua.random
        except:
            return config.DEFAULT_HEADERS['User-Agent']
    
    @retry(stop_max_attempt_number=3, wait_fixed=2000)
    def _make_request(self, method: str, url: str, **kwargs) -> requests.Response:
        """
        发起HTTP请求（带重试机制）
        
        Args:
            method: HTTP方法
            url: 请求URL
            **kwargs: 其他请求参数
            
        Returns:
            requests.Response: 响应对象
        """
        # 随机化User-Agent
        headers = kwargs.get('headers', {})
        if 'User-Agent' not in headers:
            headers['User-Agent'] = self._get_random_user_agent()
            kwargs['headers'] = headers
        
        # 添加请求延迟
        time.sleep(config.REQUEST_DELAY)
        
        try:
            response = self.session.request(method, url, verify=False, **kwargs)
            response.raise_for_status()
            
            log_api_request(url, kwargs.get('data'), response.status_code)
            return response
            
        except requests.exceptions.RequestException as e:
            log_api_request(url, kwargs.get('data'), error=str(e))
            raise
    
    def get(self, url: str, **kwargs) -> requests.Response:
        """GET请求"""
        return self._make_request('GET', url, **kwargs)
    
    def post(self, url: str, **kwargs) -> requests.Response:
        """POST请求"""
        return self._make_request('POST', url, **kwargs)
    
    def get_stock_data(self, stock_code: str, trade_date: str) -> Optional[Dict[str, Any]]:
        """
        获取股票数据
        
        Args:
            stock_code: 股票代码
            trade_date: 交易日期 (YYYYMMDD格式)
            
        Returns:
            Dict: 股票数据，失败返回None
        """
        try:
            # 构造请求参数
            params = config.API_PARAMS_TEMPLATE.copy()
            params['StockID'] = stock_code
            params['Day'] = trade_date
            
            logger.info(f"获取股票数据: {stock_code}, 日期: {trade_date}")
            
            response = self.post(config.KAIPANLA_API_BASE, data=params)
            
            if response.status_code == 200:
                data = response.json()
                logger.debug(f"股票 {stock_code} 数据获取成功")
                return data
            else:
                logger.warning(f"股票 {stock_code} 数据获取失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 数据时发生错误: {str(e)}")
            return None
    
    def get_plate_data(self, trade_date: str) -> Optional[Dict[str, Any]]:
        """
        获取板块数据
        
        Args:
            trade_date: 交易日期 (YYYYMMDD格式)
            
        Returns:
            Dict: 板块数据，失败返回None
        """
        try:
            # 使用F12抓包发现的正确参数
            params = {
                'c': 'PCArrangeData',
                'a': 'GetZSIndexPlate',
                'SelType': '2',
                'Date': trade_date,  # 使用传入的日期格式
                'PType': '2',
                'POrder': '1',
                'ZSType': '5',
                'rate': '1',
                'PStart': '',  # 空值
                'PEnd': '',    # 空值
                'PIndex': '0',
                'Pst': '20',
                'UserID': config.API_PARAMS_TEMPLATE['UserID'],
                'Token': config.API_PARAMS_TEMPLATE['Token']
            }

            logger.info(f"获取板块数据，日期: {trade_date}")

            response = self.post(config.KAIPANLA_API_BASE, data=params)

            if response.status_code == 200:
                # 检查响应内容是否为空
                response_text = response.text.strip()
                if not response_text:
                    logger.warning("板块数据API返回空内容，可能该日期无数据")
                    return None

                try:
                    data = response.json()
                    logger.debug("板块数据获取成功")
                    return data
                except ValueError as e:
                    logger.error(f"板块数据JSON解析失败: {e}, 响应内容: {response_text[:200]}")
                    return None
            else:
                logger.warning(f"板块数据获取失败，状态码: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"获取板块数据时发生错误: {str(e)}")
            return None
    
    def get_longhubang_data(self, trade_date: str) -> Optional[Dict[str, Any]]:
        """
        获取龙虎榜数据
        
        Args:
            trade_date: 交易日期 (YYYYMMDD格式)
            
        Returns:
            Dict: 龙虎榜数据，失败返回None
        """
        try:
            # 使用F12抓包发现的正确参数
            params = {
                'c': 'LongHuBang',
                'a': 'GetStockList',  # 从F12发现的正确action
                'Index': '0',
                'st': '300',
                'Time': '',  # 空值
                'UserID': config.API_PARAMS_TEMPLATE['UserID'],
                'Token': config.API_PARAMS_TEMPLATE['Token']
            }
            
            logger.info(f"获取龙虎榜数据，日期: {trade_date}")
            
            response = self.post(config.KAIPANLA_LHB_API, data=params)

            if response.status_code == 200:
                # 检查响应内容是否为空
                response_text = response.text.strip()
                if not response_text:
                    logger.warning("龙虎榜数据API返回空内容，可能该日期无数据")
                    return None

                try:
                    data = response.json()
                    logger.debug("龙虎榜数据获取成功")
                    return data
                except ValueError as e:
                    logger.error(f"龙虎榜数据JSON解析失败: {e}, 响应内容: {response_text[:200]}")
                    return None
            else:
                logger.warning(f"龙虎榜数据获取失败，状态码: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"获取龙虎榜数据时发生错误: {str(e)}")
            return None
    
    def test_connection(self) -> bool:
        """
        测试API连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            from datetime import datetime
            today = datetime.now().strftime('%Y%m%d')
            
            # 测试获取一个简单的股票数据
            result = self.get_stock_data('000001', today)
            
            if result is not None:
                logger.info("API连接测试成功")
                return True
            else:
                logger.warning("API连接测试失败")
                return False
                
        except Exception as e:
            logger.error(f"API连接测试异常: {str(e)}")
            return False

    def get_news_data(self, index: int = 0, count: int = 8) -> Optional[Dict[str, Any]]:
        """
        获取新闻快讯数据

        Args:
            index: 起始索引
            count: 获取数量

        Returns:
            Dict: 新闻数据，失败返回None
        """
        try:
            # 使用F12抓包发现的正确参数
            params = {
                'c': 'PCNewsFlash',
                'a': 'GetList',
                'Index': str(index),
                'st': str(count),
                'UserID': config.API_PARAMS_TEMPLATE['UserID'],
                'Token': config.API_PARAMS_TEMPLATE['Token']
            }

            logger.info(f"获取新闻数据，索引: {index}, 数量: {count}")

            response = self.post(config.KAIPANLA_API_BASE, data=params)

            if response.status_code == 200:
                # 检查响应内容是否为空
                response_text = response.text.strip()
                if not response_text:
                    logger.warning("新闻数据API返回空内容")
                    return None

                try:
                    data = response.json()
                    logger.debug("新闻数据获取成功")
                    return data
                except ValueError as e:
                    logger.error(f"新闻数据JSON解析失败: {e}, 响应内容: {response_text[:200]}")
                    return None
            else:
                logger.warning(f"新闻数据获取失败，状态码: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"获取新闻数据时发生错误: {str(e)}")
            return None

    def close(self):
        """关闭会话"""
        self.session.close()
        logger.info("HTTP会话已关闭")
