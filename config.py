"""
开盘啦数据采集项目配置文件
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """基础配置类"""
    
    # API配置
    KAIPANLA_API_BASE = "https://pchq.kaipanla.com/w1/api/index.php"
    KAIPANLA_LHB_API = "https://pclhb.kaipanla.com/w1/api/index.php"
    
    # 请求头配置
    DEFAULT_HEADERS = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
    }
    
    # 数据库配置
    DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite:///kaipanla_data.db')
    
    # MySQL配置 (如果使用MySQL)
    MYSQL_HOST = os.getenv('MYSQL_HOST', 'localhost')
    MYSQL_PORT = int(os.getenv('MYSQL_PORT', 3306))
    MYSQL_USER = os.getenv('MYSQL_USER', 'root')
    MYSQL_PASSWORD = os.getenv('MYSQL_PASSWORD', '')
    MYSQL_DATABASE = os.getenv('MYSQL_DATABASE', 'kaipanla')
    
    # 请求配置
    REQUEST_TIMEOUT = 30
    REQUEST_RETRY_TIMES = 3
    REQUEST_DELAY = 1  # 请求间隔秒数
    
    # 日志配置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = 'logs/kaipanla_spider.log'
    
    # 数据采集配置
    COLLECT_INTERVAL = 300  # 数据采集间隔(秒)
    BATCH_SIZE = 100  # 批量处理大小
    
    # API请求参数模板
    API_PARAMS_TEMPLATE = {
        'c': 'PCArrangeData',
        'a': 'GetHQPlate',
        'Day': '',  # 日期，格式：YYYYMMDD
        'SelType': '1,2,3,8,9,5,6,7',
        'UserID': 399083,
        'Token': '71aef0e806e61ad3169ddc9473e37886'
    }
    
    @classmethod
    def get_mysql_url(cls):
        """获取MySQL连接URL"""
        return f"mysql+pymysql://{cls.MYSQL_USER}:{cls.MYSQL_PASSWORD}@{cls.MYSQL_HOST}:{cls.MYSQL_PORT}/{cls.MYSQL_DATABASE}?charset=utf8mb4"

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    LOG_LEVEL = 'DEBUG'

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    LOG_LEVEL = 'INFO'

# 根据环境变量选择配置
config_map = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

def get_config():
    """获取当前配置"""
    env = os.getenv('FLASK_ENV', 'default')
    return config_map.get(env, DevelopmentConfig)
