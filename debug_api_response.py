#!/usr/bin/env python3
"""
调试API响应内容
"""
import requests
from config import get_config

config = get_config()

def debug_plate_api():
    """调试板块数据API"""
    print("=== 调试板块数据API ===")
    
    url = "https://pchq.kaipanla.com/w1/api/index.php"
    params = {
        'UserID': config.API_PARAMS_TEMPLATE['UserID'],
        'Token': config.API_PARAMS_TEMPLATE['Token'],
        'act': 'getPlateList',
        'day': '20250630'
    }
    
    try:
        response = requests.get(url, params=params, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {response.headers}")
        print(f"响应内容 (前500字符): {response.text[:500]}")
        print(f"响应内容 (完整): {response.text}")
        
        # 尝试解析JSON
        try:
            data = response.json()
            print(f"JSON解析成功: {type(data)}")
            print(f"JSON内容: {data}")
        except Exception as e:
            print(f"JSON解析失败: {e}")
            
    except Exception as e:
        print(f"请求失败: {e}")

def debug_longhubang_api():
    """调试龙虎榜数据API"""
    print("\n=== 调试龙虎榜数据API ===")
    
    url = "https://pclhb.kaipanla.com/w1/api/index.php"
    params = {
        'UserID': config.API_PARAMS_TEMPLATE['UserID'],
        'Token': config.API_PARAMS_TEMPLATE['Token'],
        'act': 'getLongHuBang',
        'day': '20250630'
    }
    
    try:
        response = requests.get(url, params=params, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {response.headers}")
        print(f"响应内容 (前500字符): {response.text[:500]}")
        print(f"响应内容 (完整): {response.text}")
        
        # 尝试解析JSON
        try:
            data = response.json()
            print(f"JSON解析成功: {type(data)}")
            print(f"JSON内容: {data}")
        except Exception as e:
            print(f"JSON解析失败: {e}")
            
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    debug_plate_api()
    debug_longhubang_api()
