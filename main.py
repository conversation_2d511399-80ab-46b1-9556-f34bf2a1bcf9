#!/usr/bin/env python3
"""
开盘啦数据采集项目主程序
"""
import sys
import argparse
import signal
from datetime import datetime

from core.scheduler import TaskScheduler
from core.data_collector import DataCollector
from core.data_processor import DataProcessor
from core.http_client import KaipanlaClient
from models.database import init_database
from utils.logger import setup_logger
from utils.error_handler import global_error_collector
from config import get_config

config = get_config()
logger = setup_logger(__name__)

class KaipanlaSpider:
    """开盘啦数据采集主程序"""
    
    def __init__(self):
        self.scheduler = None
        self.is_running = False
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"接收到信号 {signum}，准备退出...")
        self.stop()
        sys.exit(0)
    
    def start_scheduler(self):
        """启动调度器模式"""
        logger.info("启动开盘啦数据采集调度器")
        
        try:
            self.scheduler = TaskScheduler()
            self.scheduler.start()
            self.is_running = True
            
            logger.info("调度器启动成功，按 Ctrl+C 退出")
            
            # 保持主线程运行
            while self.is_running:
                import time
                time.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("接收到中断信号")
        except Exception as e:
            logger.error(f"调度器运行异常: {str(e)}")
        finally:
            self.stop()
    
    def stop(self):
        """停止程序"""
        self.is_running = False
        if self.scheduler:
            self.scheduler.stop()
            self.scheduler = None
        logger.info("程序已停止")
    
    def test_connection(self):
        """测试API连接"""
        logger.info("测试开盘啦API连接...")
        
        try:
            client = KaipanlaClient()
            success = client.test_connection()
            
            if success:
                logger.info("✅ API连接测试成功")
                return True
            else:
                logger.error("❌ API连接测试失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ API连接测试异常: {str(e)}")
            return False
    
    def collect_today_data(self):
        """采集今日数据"""
        trade_date = datetime.now().strftime('%Y%m%d')
        logger.info(f"开始采集今日数据: {trade_date}")
        
        try:
            with DataCollector() as collector:
                # 获取股票列表
                stock_list = collector.get_stock_list_from_tushare()
                if not stock_list:
                    logger.error("未获取到股票列表")
                    return False
                
                logger.info(f"获取到 {len(stock_list)} 只股票")
                
                # 批量采集股票数据
                results = collector.batch_collect_stocks(stock_list[:10], trade_date)  # 测试时只采集前10只
                logger.info(f"股票数据采集结果: {results}")
                
                # 采集板块数据
                plate_success = collector.collect_plate_data(trade_date)
                logger.info(f"板块数据采集: {'成功' if plate_success else '失败'}")
                
                # 采集龙虎榜数据
                lhb_success = collector.collect_longhubang_data(trade_date)
                logger.info(f"龙虎榜数据采集: {'成功' if lhb_success else '失败'}")
                
                return True
                
        except Exception as e:
            logger.error(f"数据采集失败: {str(e)}")
            return False
    
    def clean_data(self, trade_date: str = None):
        """清洗数据"""
        if not trade_date:
            trade_date = datetime.now().strftime('%Y%m%d')
        
        logger.info(f"开始清洗数据: {trade_date}")
        
        try:
            with DataProcessor() as processor:
                # 清洗股票数据
                clean_results = processor.clean_stock_data(trade_date)
                logger.info(f"数据清洗结果: {clean_results}")
                
                # 删除重复数据
                tables = ['stock_quote', 'stock_tag', 'stock_concept']
                for table in tables:
                    deleted = processor.remove_duplicates(table, trade_date)
                    logger.info(f"表 {table} 删除重复数据 {deleted} 条")
                
                return True
                
        except Exception as e:
            logger.error(f"数据清洗失败: {str(e)}")
            return False
    
    def export_data(self, trade_date: str = None, output_path: str = None):
        """导出数据"""
        if not trade_date:
            trade_date = datetime.now().strftime('%Y%m%d')
        
        logger.info(f"开始导出数据: {trade_date}")
        
        try:
            with DataProcessor() as processor:
                # 导出数据到Excel
                file_path = processor.export_to_excel(trade_date, output_path)
                logger.info(f"数据导出成功: {file_path}")
                
                # 显示数据统计
                stats = processor.get_data_statistics(trade_date)
                logger.info(f"数据统计: {stats}")
                
                return file_path
                
        except Exception as e:
            logger.error(f"数据导出失败: {str(e)}")
            return None
    
    def show_status(self):
        """显示系统状态"""
        logger.info("=== 系统状态 ===")
        
        # 显示配置信息
        logger.info(f"数据库URL: {config.DATABASE_URL}")
        logger.info(f"日志级别: {config.LOG_LEVEL}")
        
        # 显示调度器状态
        if self.scheduler:
            status = self.scheduler.get_task_status()
            logger.info(f"调度器状态: {status}")
        
        # 显示错误统计
        error_summary = global_error_collector.get_summary()
        logger.info(f"错误统计: {error_summary}")
        
        # 显示数据统计
        try:
            with DataProcessor() as processor:
                stats = processor.get_data_statistics()
                logger.info(f"数据统计: {stats}")
        except Exception as e:
            logger.error(f"获取数据统计失败: {str(e)}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='开盘啦数据采集工具')
    parser.add_argument('command', choices=[
        'start', 'test', 'collect', 'clean', 'export', 'status', 'init'
    ], help='执行的命令')
    parser.add_argument('--date', help='指定日期 (YYYYMMDD格式)')
    parser.add_argument('--output', help='输出文件路径')
    
    args = parser.parse_args()
    
    spider = KaipanlaSpider()
    
    try:
        if args.command == 'init':
            # 初始化数据库
            logger.info("初始化数据库...")
            init_database()
            logger.info("数据库初始化完成")
            
        elif args.command == 'start':
            # 启动调度器
            spider.start_scheduler()
            
        elif args.command == 'test':
            # 测试连接
            success = spider.test_connection()
            sys.exit(0 if success else 1)
            
        elif args.command == 'collect':
            # 采集数据
            success = spider.collect_today_data()
            sys.exit(0 if success else 1)
            
        elif args.command == 'clean':
            # 清洗数据
            success = spider.clean_data(args.date)
            sys.exit(0 if success else 1)
            
        elif args.command == 'export':
            # 导出数据
            file_path = spider.export_data(args.date, args.output)
            sys.exit(0 if file_path else 1)
            
        elif args.command == 'status':
            # 显示状态
            spider.show_status()
            
    except Exception as e:
        logger.error(f"程序执行异常: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
