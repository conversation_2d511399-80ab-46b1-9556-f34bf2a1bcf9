#!/usr/bin/env python3
"""
创建完整的股票列表并验证有效性
"""
from core.http_client import KaipanlaClient
from typing import List
import time
import random

def generate_all_stock_codes() -> List[str]:
    """生成所有可能的股票代码"""
    print("🔢 生成所有可能的股票代码...")
    
    stock_codes = []
    
    # 深圳主板 000001-000999
    print("   生成深圳主板 000xxx...")
    for i in range(1, 1000):
        stock_codes.append(f"000{i:03d}")
    
    # 深圳中小板 002001-002999  
    print("   生成深圳中小板 002xxx...")
    for i in range(1, 1000):
        stock_codes.append(f"002{i:03d}")
    
    # 深圳创业板 300001-300999
    print("   生成深圳创业板 300xxx...")
    for i in range(1, 1000):
        stock_codes.append(f"300{i:03d}")
    
    # 深圳创业板扩展 301001-301999
    print("   生成深圳创业板扩展 301xxx...")
    for i in range(1, 1000):
        stock_codes.append(f"301{i:03d}")
    
    # 上海主板 600000-603999
    print("   生成上海主板 60xxxx...")
    for i in range(600000, 604000):
        stock_codes.append(f"{i}")
    
    # 上海科创板 688001-688999
    print("   生成上海科创板 688xxx...")
    for i in range(1, 1000):
        stock_codes.append(f"688{i:03d}")
    
    # 北交所 430001-899999 (选择性生成)
    print("   生成北交所部分代码...")
    for i in range(430001, 430100):  # 只生成一小部分测试
        stock_codes.append(f"{i}")
    
    print(f"✅ 总共生成了 {len(stock_codes)} 个股票代码")
    return stock_codes

def validate_stock_codes_batch(client: KaipanlaClient, stock_codes: List[str], batch_size: int = 50) -> List[str]:
    """批量验证股票代码有效性"""
    print(f"🔍 开始验证 {len(stock_codes)} 个股票代码...")
    
    valid_stocks = []
    total_batches = (len(stock_codes) + batch_size - 1) // batch_size
    
    for batch_idx in range(0, len(stock_codes), batch_size):
        batch = stock_codes[batch_idx:batch_idx + batch_size]
        current_batch = batch_idx // batch_size + 1
        
        print(f"   验证第 {current_batch}/{total_batches} 批 ({len(batch)} 个代码)...")
        
        batch_valid = []
        for code in batch:
            try:
                # 使用今天日期测试
                data = client.get_stock_data(code, '20250630')
                if data and isinstance(data, dict) and len(data) > 0:
                    # 检查是否有有效数据
                    if any(key in data for key in ['name', 'close', 'open', 'high', 'low']):
                        batch_valid.append(code)
                        if len(batch_valid) <= 3:  # 只显示前3个
                            print(f"      ✅ {code} - 有效")
                
                # 避免请求过快
                time.sleep(0.1)
                
            except Exception as e:
                # 忽略错误，继续下一个
                pass
        
        valid_stocks.extend(batch_valid)
        print(f"      本批有效: {len(batch_valid)} 个，累计: {len(valid_stocks)} 个")
        
        # 每批之间稍作休息
        if current_batch < total_batches:
            time.sleep(1)
    
    print(f"✅ 验证完成，找到 {len(valid_stocks)} 个有效股票代码")
    return valid_stocks

def create_comprehensive_stock_list():
    """创建综合股票列表"""
    print("🚀 === 创建完整A股股票列表 ===\n")
    
    # 1. 生成所有可能的股票代码
    all_codes = generate_all_stock_codes()
    
    # 2. 随机选择一部分进行验证（避免请求过多）
    print(f"\n🎲 从 {len(all_codes)} 个代码中随机选择 500 个进行验证...")
    random.shuffle(all_codes)
    test_codes = all_codes[:500]
    
    # 3. 验证股票代码
    client = KaipanlaClient()
    try:
        valid_stocks = validate_stock_codes_batch(client, test_codes, batch_size=20)
        
        if valid_stocks:
            print(f"\n🎯 === 验证结果 ===")
            print(f"✅ 找到 {len(valid_stocks)} 个有效股票代码")
            print(f"📝 有效股票代码示例: {valid_stocks[:20]}")
            
            # 4. 基于验证结果，扩展到完整列表
            print(f"\n📈 基于验证结果推断完整股票列表...")
            
            # 分析有效代码的模式
            valid_prefixes = set()
            for code in valid_stocks:
                if len(code) == 6:
                    valid_prefixes.add(code[:3])
                elif len(code) == 6:
                    valid_prefixes.add(code[:3])
            
            print(f"   发现有效前缀: {sorted(valid_prefixes)}")
            
            # 基于有效前缀生成完整列表
            full_stock_list = []
            for prefix in sorted(valid_prefixes):
                if prefix in ['000', '002', '300', '301', '688']:
                    # 3位前缀 + 3位数字
                    for i in range(1, 1000):
                        full_stock_list.append(f"{prefix}{i:03d}")
                elif prefix in ['600', '601', '603']:
                    # 6位数字
                    start = int(prefix + '000')
                    end = int(prefix + '999')
                    for i in range(start, end + 1):
                        full_stock_list.append(f"{i}")
            
            # 5. 保存结果
            print(f"\n💾 保存股票列表...")
            with open('full_stock_list.txt', 'w', encoding='utf-8') as f:
                for code in full_stock_list:
                    f.write(f"{code}\n")
            
            with open('verified_stock_list.txt', 'w', encoding='utf-8') as f:
                for code in valid_stocks:
                    f.write(f"{code}\n")
            
            print(f"✅ 完整股票列表已保存到 full_stock_list.txt ({len(full_stock_list)} 个)")
            print(f"✅ 验证有效列表已保存到 verified_stock_list.txt ({len(valid_stocks)} 个)")
            
            return full_stock_list
        else:
            print("❌ 未找到有效的股票代码")
            return []
            
    finally:
        client.close()

if __name__ == "__main__":
    create_comprehensive_stock_list()
