#!/usr/bin/env python3
"""
检查板块强度数据结构
"""
from core.http_client import KaipanlaClient

def check_plate_strength():
    """检查板块强度数据"""
    print("🔍 === 检查板块强度数据结构 ===\n")
    
    client = KaipanlaClient()
    try:
        # 获取板块强度数据 (ZSType: 7, PType: 1)
        data = client.get_core_plate_data()
        
        if not data:
            print("❌ 板块强度数据为空")
            return
        
        print(f"✅ 数据获取成功")
        print(f"数据类型: {type(data)}")
        print(f"数据键: {list(data.keys())}\n")
        
        if 'plates' in data and isinstance(data['plates'], dict):
            plates = data['plates']
            print(f"plates类型: {type(plates)}")
            print(f"plates键: {list(plates.keys())}\n")
            
            if 'list' in plates and isinstance(plates['list'], list):
                plate_list = plates['list']
                print(f"📊 板块数量: {len(plate_list)}")
                
                if len(plate_list) > 0:
                    print(f"每个板块数据字段数量: {len(plate_list[0])}\n")
                    
                    # 显示前5个板块的详细数据
                    print("🎯 === 前5个板块详细数据 ===")
                    for i, plate in enumerate(plate_list[:5]):
                        if isinstance(plate, list):
                            print(f"\n板块 {i+1}:")
                            print(f"  完整数据: {plate}")
                            print(f"  字段数量: {len(plate)}")
                            
                            # 尝试解析关键字段
                            if len(plate) >= 15:
                                print(f"  [0] 板块代码: {plate[0]}")
                                print(f"  [1] 板块名称: {plate[1]}")
                                print(f"  [2] 可能是强度: {plate[2]}")
                                print(f"  [3] 可能是涨速: {plate[3]}")
                                print(f"  [4] 字段4: {plate[4]}")
                                print(f"  [5] 字段5: {plate[5]}")
                                print(f"  [6] 字段6: {plate[6]}")
                                print(f"  [7] 字段7: {plate[7]}")
                                print(f"  [8] 字段8: {plate[8]}")
                                print(f"  [9] 字段9: {plate[9]}")
                                if len(plate) > 10:
                                    print(f"  [10] 字段10: {plate[10]}")
                                if len(plate) > 11:
                                    print(f"  [11] 字段11: {plate[11]}")
                                if len(plate) > 12:
                                    print(f"  [12] 字段12: {plate[12]}")
                                if len(plate) > 13:
                                    print(f"  [13] 字段13: {plate[13]}")
                                if len(plate) > 14:
                                    print(f"  [14] 字段14: {plate[14]}")
                    
                    print(f"\n📋 === 数据字段分析 ===")
                    print("根据你提供的板块强度表格，字段应该包括：")
                    print("- 名称、强度、涨速、成交额、主力净额、主力买入、主力卖出、量比、流通市值")
                    print("让我们对比实际数据来确定字段映射关系")
                    
            else:
                print("❌ plates中没有list字段或格式不正确")
        else:
            print("❌ 数据中没有plates字段或格式不正确")
            
    except Exception as e:
        print(f"❌ 检查板块强度数据时发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        client.close()

if __name__ == "__main__":
    check_plate_strength()
