#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
开盘啦登录管理模块
处理用户登录、token获取和维护
"""

import requests
import json
import time
import re
from typing import Dict, Optional, Tuple
from urllib.parse import urljoin
from bs4 import BeautifulSoup
from fake_useragent import UserAgent

from config import get_config
from utils.logger import setup_logger

config = get_config()
logger = setup_logger(__name__)

class KaipanlaLoginManager:
    """开盘啦登录管理器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.ua = UserAgent()
        self.base_url = "https://www.kaipanla.com"
        self.login_url = "https://www.kaipanla.com/login"
        self.api_base = config.KAIPANLA_API_BASE
        
        # 设置会话
        self._setup_session()
        
        # 存储登录信息
        self.user_id = None
        self.token = None
        self.is_logged_in = False
        
    def _setup_session(self):
        """设置会话配置"""
        self.session.headers.update({
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # 禁用SSL验证警告
        requests.packages.urllib3.disable_warnings()
    
    def get_login_page(self) -> Optional[str]:
        """
        获取登录页面
        
        Returns:
            str: 登录页面HTML内容
        """
        try:
            logger.info("正在获取登录页面...")
            response = self.session.get(self.login_url, verify=False, timeout=30)
            response.raise_for_status()
            
            logger.info("登录页面获取成功")
            return response.text
            
        except Exception as e:
            logger.error(f"获取登录页面失败: {str(e)}")
            return None
    
    def extract_login_params(self, html_content: str) -> Dict[str, str]:
        """
        从登录页面提取必要的参数
        
        Args:
            html_content: 登录页面HTML内容
            
        Returns:
            Dict: 登录参数
        """
        params = {}
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找CSRF token或其他隐藏字段
            csrf_token = soup.find('input', {'name': '_token'})
            if csrf_token:
                params['_token'] = csrf_token.get('value', '')
            
            # 查找其他可能的隐藏字段
            hidden_inputs = soup.find_all('input', {'type': 'hidden'})
            for input_field in hidden_inputs:
                name = input_field.get('name')
                value = input_field.get('value', '')
                if name:
                    params[name] = value
            
            logger.debug(f"提取到登录参数: {list(params.keys())}")
            return params
            
        except Exception as e:
            logger.error(f"提取登录参数失败: {str(e)}")
            return {}
    
    def simulate_login(self, username: str, password: str) -> bool:
        """
        模拟用户登录
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            bool: 登录是否成功
        """
        try:
            logger.info(f"开始模拟登录，用户名: {username}")
            
            # 1. 获取登录页面
            login_page = self.get_login_page()
            if not login_page:
                return False
            
            # 2. 提取登录参数
            login_params = self.extract_login_params(login_page)
            
            # 3. 构造登录数据
            login_data = {
                'username': username,
                'password': password,
                **login_params
            }
            
            # 4. 发送登录请求
            login_response = self.session.post(
                self.login_url,
                data=login_data,
                verify=False,
                timeout=30,
                allow_redirects=True
            )
            
            # 5. 检查登录结果
            if self._check_login_success(login_response):
                logger.info("登录成功")
                self.is_logged_in = True
                
                # 6. 获取用户ID和Token
                self._extract_user_credentials()
                return True
            else:
                logger.warning("登录失败")
                return False
                
        except Exception as e:
            logger.error(f"模拟登录过程中发生错误: {str(e)}")
            return False
    
    def _check_login_success(self, response: requests.Response) -> bool:
        """
        检查登录是否成功
        
        Args:
            response: 登录响应
            
        Returns:
            bool: 是否登录成功
        """
        try:
            # 检查响应状态码
            if response.status_code != 200:
                return False
            
            # 检查是否重定向到主页或用户页面
            if 'login' not in response.url.lower():
                return True
            
            # 检查响应内容中是否包含登录成功的标识
            content = response.text.lower()
            success_indicators = ['dashboard', 'logout', '退出', '用户中心', 'user']
            failure_indicators = ['login', 'error', '错误', '失败', 'failed']
            
            has_success = any(indicator in content for indicator in success_indicators)
            has_failure = any(indicator in content for indicator in failure_indicators)
            
            return has_success and not has_failure
            
        except Exception as e:
            logger.error(f"检查登录状态时发生错误: {str(e)}")
            return False
    
    def _extract_user_credentials(self):
        """从登录后的页面提取用户凭据"""
        try:
            # 尝试访问用户信息页面或API
            user_info_url = urljoin(self.base_url, '/user/info')
            response = self.session.get(user_info_url, verify=False, timeout=30)
            
            if response.status_code == 200:
                # 尝试从响应中提取用户ID和Token
                content = response.text
                
                # 使用正则表达式查找用户ID
                user_id_match = re.search(r'user[_-]?id["\']?\s*[:=]\s*["\']?(\d+)', content, re.IGNORECASE)
                if user_id_match:
                    self.user_id = int(user_id_match.group(1))
                    logger.info(f"提取到用户ID: {self.user_id}")
                
                # 查找Token
                token_match = re.search(r'token["\']?\s*[:=]\s*["\']?([a-f0-9]{32})', content, re.IGNORECASE)
                if token_match:
                    self.token = token_match.group(1)
                    logger.info(f"提取到Token: {self.token[:8]}...")
            
        except Exception as e:
            logger.error(f"提取用户凭据时发生错误: {str(e)}")
    
    def get_credentials_from_browser(self) -> Tuple[Optional[int], Optional[str]]:
        """
        从浏览器开发者工具获取凭据的指导方法
        
        Returns:
            Tuple: (user_id, token)
        """
        instructions = """
        请按照以下步骤从浏览器获取有效的用户凭据：
        
        1. 打开浏览器，访问 https://www.kaipanla.com
        2. 登录您的账号
        3. 按F12打开开发者工具
        4. 切换到 Network (网络) 标签
        5. 在网站上进行任何操作（如查看股票数据）
        6. 在网络请求中找到发送到 API 的请求
        7. 查看请求参数，找到 UserID 和 Token
        8. 将这些值更新到 config.py 文件中
        
        示例请求参数：
        {
            "UserID": 123456,
            "Token": "abcdef1234567890abcdef1234567890"
        }
        """
        
        print(instructions)
        logger.info("请按照控制台输出的指导获取用户凭据")
        
        return None, None
    
    def update_config_credentials(self, user_id: int, token: str) -> bool:
        """
        更新配置文件中的凭据
        
        Args:
            user_id: 用户ID
            token: 用户Token
            
        Returns:
            bool: 更新是否成功
        """
        try:
            # 读取配置文件
            with open('config.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 更新UserID
            content = re.sub(
                r"'UserID':\s*\d+",
                f"'UserID': {user_id}",
                content
            )
            
            # 更新Token
            content = re.sub(
                r"'Token':\s*'[^']*'",
                f"'Token': '{token}'",
                content
            )
            
            # 写回配置文件
            with open('config.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info("配置文件更新成功")
            return True
            
        except Exception as e:
            logger.error(f"更新配置文件失败: {str(e)}")
            return False
    
    def test_credentials(self, user_id: int, token: str) -> bool:
        """
        测试凭据是否有效
        
        Args:
            user_id: 用户ID
            token: 用户Token
            
        Returns:
            bool: 凭据是否有效
        """
        try:
            from datetime import datetime
            
            # 构造测试请求
            test_params = {
                'c': 'PCArrangeData',
                'a': 'GetHQPlate',
                'Day': datetime.now().strftime('%Y%m%d'),
                'SelType': '1,2,3,8,9,5,6,7',
                'UserID': user_id,
                'Token': token,
                'StockID': '000001'
            }
            
            response = self.session.post(
                self.api_base,
                data=test_params,
                verify=False,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # 检查是否返回错误
                if isinstance(data, dict) and data.get('errcode') == '1001':
                    logger.warning("凭据已失效")
                    return False
                else:
                    logger.info("凭据测试成功")
                    return True
            else:
                logger.warning(f"凭据测试失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"测试凭据时发生错误: {str(e)}")
            return False
    
    def close(self):
        """关闭会话"""
        self.session.close()
        logger.info("登录管理器会话已关闭")
