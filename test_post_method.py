#!/usr/bin/env python3
"""
测试POST方法调用板块和龙虎榜API
"""
import requests
from config import get_config

config = get_config()

def test_plate_api_post():
    """测试板块API使用POST方法"""
    print("=== 测试板块API - POST方法 ===")
    
    url = "https://pchq.kaipanla.com/w1/api/index.php"
    
    # 测试不同的参数组合
    param_combinations = [
        # 原始参数
        {'c': 'PCArrangeData', 'a': 'GetPlateList', 'Day': '20250630'},
        # 尝试不同的action
        {'act': 'getPlateList', 'day': '20250630'},
        {'act': 'GetPlateList', 'Day': '20250630'},
        # 尝试股票数据的参数格式
        {'StockID': 'ALL', 'Day': '20250630'},
        # 尝试不同的controller
        {'c': 'PlateData', 'a': 'GetList', 'Day': '20250630'},
    ]
    
    base_params = {
        'UserID': config.API_PARAMS_TEMPLATE['UserID'],
        'Token': config.API_PARAMS_TEMPLATE['Token']
    }
    
    for i, params in enumerate(param_combinations):
        print(f"\n--- POST测试组合 {i+1} ---")
        test_params = base_params.copy()
        test_params.update(params)
        print(f"参数: {test_params}")
        
        try:
            response = requests.post(url, data=test_params, timeout=10)
            print(f"状态码: {response.status_code}")
            
            content = response.text.strip()
            print(f"响应长度: {len(content)}")
            
            if content and len(content) > 10:  # 不只是换行符
                print(f"响应内容 (前200字符): {content[:200]}")
                try:
                    data = response.json()
                    print(f"JSON解析成功: {type(data)}")
                    if isinstance(data, dict):
                        print(f"JSON键: {list(data.keys())}")
                        if 'errcode' in data:
                            print(f"错误码: {data['errcode']}")
                except:
                    print("JSON解析失败")
            else:
                print("响应为空或只有换行符")
                
        except Exception as e:
            print(f"请求失败: {e}")

def test_longhubang_api_post():
    """测试龙虎榜API使用POST方法"""
    print("\n\n=== 测试龙虎榜API - POST方法 ===")
    
    url = "https://pclhb.kaipanla.com/w1/api/index.php"
    
    # 测试不同的参数组合
    param_combinations = [
        # 原始参数
        {'c': 'LongHuBang', 'a': 'GetLongHuBangData', 'Day': '20250630'},
        # 尝试不同的action
        {'act': 'getLongHuBang', 'day': '20250630'},
        {'act': 'GetLongHuBang', 'Day': '20250630'},
        # 尝试不同的controller
        {'c': 'PCArrangeData', 'a': 'GetLongHuBang', 'Day': '20250630'},
        # 尝试股票数据的参数格式
        {'StockID': 'LHB', 'Day': '20250630'},
    ]
    
    base_params = {
        'UserID': config.API_PARAMS_TEMPLATE['UserID'],
        'Token': config.API_PARAMS_TEMPLATE['Token']
    }
    
    for i, params in enumerate(param_combinations):
        print(f"\n--- POST测试组合 {i+1} ---")
        test_params = base_params.copy()
        test_params.update(params)
        print(f"参数: {test_params}")
        
        try:
            response = requests.post(url, data=test_params, timeout=10)
            print(f"状态码: {response.status_code}")
            
            content = response.text.strip()
            print(f"响应长度: {len(content)}")
            
            if content and len(content) > 10:  # 不只是换行符
                print(f"响应内容 (前200字符): {content[:200]}")
                try:
                    data = response.json()
                    print(f"JSON解析成功: {type(data)}")
                    if isinstance(data, dict):
                        print(f"JSON键: {list(data.keys())}")
                        if 'errcode' in data:
                            print(f"错误码: {data['errcode']}")
                except:
                    print("JSON解析失败")
            else:
                print("响应为空或只有换行符")
                
        except Exception as e:
            print(f"请求失败: {e}")

def test_different_dates():
    """测试不同日期的数据"""
    print("\n\n=== 测试不同日期的数据 - POST方法 ===")
    
    dates = ['20250628', '20250627', '20250626', '20250625', '20250624']  # 尝试工作日
    
    base_params = {
        'UserID': config.API_PARAMS_TEMPLATE['UserID'],
        'Token': config.API_PARAMS_TEMPLATE['Token']
    }
    
    for date in dates:
        print(f"\n--- 测试日期: {date} ---")
        
        # 测试板块数据
        params = base_params.copy()
        params.update({'c': 'PCArrangeData', 'a': 'GetPlateList', 'Day': date})
        
        try:
            response = requests.post("https://pchq.kaipanla.com/w1/api/index.php", data=params, timeout=10)
            content = response.text.strip()
            print(f"板块数据 - 状态码: {response.status_code}, 响应长度: {len(content)}")
            if len(content) > 10:
                print(f"板块数据有内容！")
                
        except Exception as e:
            print(f"板块数据测试失败: {e}")

if __name__ == "__main__":
    test_plate_api_post()
    test_longhubang_api_post()
    test_different_dates()
