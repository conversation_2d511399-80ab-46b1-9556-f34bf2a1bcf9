#!/usr/bin/env python3
"""
开盘啦数据查看工具
提供多种方式查看收集到的数据
"""
import sys
from datetime import datetime, timedelta
from sqlalchemy import create_engine, func
from sqlalchemy.orm import sessionmaker
from tabulate import tabulate
import pandas as pd

from config import Config
from models.database import StockQuote, PlateInfo, LongHuBang, PlateStrength

class DataViewer:
    """数据查看器"""
    
    def __init__(self):
        """初始化数据查看器"""
        self.engine = create_engine(Config.DATABASE_URL)
        Session = sessionmaker(bind=self.engine)
        self.session = Session()
    
    def show_data_summary(self):
        """显示数据概览"""
        print("📊 === 开盘啦数据库概览 ===\n")
        
        # 统计各类数据数量
        stock_count = self.session.query(StockQuote).count()
        plate_count = self.session.query(PlateInfo).count()
        lhb_count = self.session.query(LongHuBang).count()
        plate_strength_count = self.session.query(PlateStrength).count()

        print(f"📈 股票数据: {stock_count} 条")
        print(f"📊 板块数据: {plate_count} 条")
        print(f"🐉 龙虎榜数据: {lhb_count} 条")
        print(f"💪 板块强度数据: {plate_strength_count} 条")
        print(f"📅 总计: {stock_count + plate_count + lhb_count + plate_strength_count} 条数据\n")
        
        # 显示最新数据日期
        if stock_count > 0:
            latest_stock = self.session.query(StockQuote).order_by(StockQuote.trade_date.desc()).first()
            print(f"📈 最新股票数据日期: {latest_stock.trade_date}")

        if plate_count > 0:
            latest_plate = self.session.query(PlateInfo).order_by(PlateInfo.trade_date.desc()).first()
            print(f"📊 最新板块数据日期: {latest_plate.trade_date}")

        if lhb_count > 0:
            latest_lhb = self.session.query(LongHuBang).order_by(LongHuBang.trade_date.desc()).first()
            print(f"🐉 最新龙虎榜数据日期: {latest_lhb.trade_date}")

        if plate_strength_count > 0:
            latest_strength = self.session.query(PlateStrength).order_by(PlateStrength.trade_date.desc()).first()
            print(f"💪 最新板块强度数据日期: {latest_strength.trade_date}")
    
    def show_stock_data(self, limit=10, stock_code=None, trade_date=None):
        """显示股票数据"""
        print(f"📈 === 股票数据 (最新{limit}条) ===\n")

        query = self.session.query(StockQuote)

        if stock_code:
            query = query.filter(StockQuote.symbol == stock_code)
        if trade_date:
            query = query.filter(StockQuote.trade_date == trade_date)

        stocks = query.order_by(StockQuote.created_at.desc()).limit(limit).all()

        if not stocks:
            print("❌ 没有找到股票数据")
            return

        # 准备表格数据
        headers = ["股票代码", "交易日期", "开盘价", "收盘价", "最高价", "最低价", "涨跌幅%", "成交量", "创建时间"]
        rows = []

        for stock in stocks:
            rows.append([
                stock.symbol,
                stock.trade_date,
                f"{stock.open_price:.2f}" if stock.open_price else "N/A",
                f"{stock.close_price:.2f}" if stock.close_price else "N/A",
                f"{stock.high_price:.2f}" if stock.high_price else "N/A",
                f"{stock.low_price:.2f}" if stock.low_price else "N/A",
                f"{stock.change_rate:.2f}%" if stock.change_rate else "N/A",
                f"{stock.volume:,}" if stock.volume else "N/A",
                stock.created_at.strftime("%m-%d %H:%M")
            ])

        print(tabulate(rows, headers=headers, tablefmt="grid"))
    
    def show_plate_data(self, limit=10, trade_date=None):
        """显示板块数据"""
        print(f"📊 === 板块数据 (最新{limit}条) ===\n")
        
        query = self.session.query(PlateInfo)
        
        if trade_date:
            query = query.filter(PlateInfo.trade_date == trade_date)
        
        plates = query.order_by(PlateInfo.change_rate.desc()).limit(limit).all()
        
        if not plates:
            print("❌ 没有找到板块数据")
            return
        
        # 准备表格数据
        headers = ["板块代码", "板块名称", "板块类型", "交易日期", "涨跌幅%", "创建时间"]
        rows = []
        
        for plate in plates:
            rows.append([
                plate.plate_code,
                plate.plate_name[:15] + "..." if len(plate.plate_name) > 15 else plate.plate_name,
                plate.plate_type,
                plate.trade_date,
                f"{plate.change_rate:.2f}%" if plate.change_rate else "N/A",
                plate.created_at.strftime("%m-%d %H:%M")
            ])
        
        print(tabulate(rows, headers=headers, tablefmt="grid"))
    
    def show_longhubang_data(self, limit=10, trade_date=None):
        """显示龙虎榜数据"""
        print(f"🐉 === 龙虎榜数据 (最新{limit}条) ===\n")

        query = self.session.query(LongHuBang)

        if trade_date:
            query = query.filter(LongHuBang.trade_date == trade_date)

        lhb_data = query.order_by(LongHuBang.created_at.desc()).limit(limit).all()

        if not lhb_data:
            print("❌ 没有找到龙虎榜数据")
            return

        # 准备表格数据
        headers = ["股票代码", "交易日期", "上榜原因", "买入金额", "卖出金额", "净买入", "营业部", "创建时间"]
        rows = []

        for lhb in lhb_data:
            rows.append([
                lhb.symbol,
                lhb.trade_date,
                lhb.reason[:15] + "..." if lhb.reason and len(lhb.reason) > 15 else (lhb.reason or "N/A"),
                f"{lhb.buy_amount:,.0f}" if lhb.buy_amount else "N/A",
                f"{lhb.sell_amount:,.0f}" if lhb.sell_amount else "N/A",
                f"{lhb.net_amount:,.0f}" if lhb.net_amount else "N/A",
                lhb.department[:10] + "..." if lhb.department and len(lhb.department) > 10 else (lhb.department or "N/A"),
                lhb.created_at.strftime("%m-%d %H:%M")
            ])

        print(tabulate(rows, headers=headers, tablefmt="grid"))

    def show_plate_strength_data(self, limit=10, trade_date=None):
        """显示板块强度数据"""
        print(f"💪 === 板块强度数据 (最新{limit}条) ===\n")

        query = self.session.query(PlateStrength)

        if trade_date:
            query = query.filter(PlateStrength.trade_date == trade_date)

        plates = query.order_by(PlateStrength.strength.desc()).limit(limit).all()

        if not plates:
            print("❌ 没有找到板块强度数据")
            return

        # 准备表格数据
        headers = ["板块名称", "强度", "涨速%", "成交额(亿)", "主力净额(亿)", "主力买入(亿)", "主力卖出(亿)", "量比", "流通市值(亿)"]
        rows = []

        for plate in plates:
            rows.append([
                plate.plate_name[:12] + "..." if len(plate.plate_name) > 12 else plate.plate_name,
                f"{plate.strength:.2f}" if plate.strength else "N/A",
                f"{plate.rise_speed:.2f}%" if plate.rise_speed else "N/A",
                f"{plate.turnover/100000000:.2f}" if plate.turnover else "N/A",  # 转换为亿
                f"{plate.main_net_amount/100000000:.2f}" if plate.main_net_amount else "N/A",  # 转换为亿
                f"{plate.main_buy_amount/100000000:.2f}" if plate.main_buy_amount else "N/A",  # 转换为亿
                f"{plate.main_sell_amount/100000000:.2f}" if plate.main_sell_amount else "N/A",  # 转换为亿
                f"{plate.volume_ratio:.2f}" if plate.volume_ratio else "N/A",
                f"{plate.market_cap/100000000:.2f}" if plate.market_cap else "N/A"  # 转换为亿
            ])

        print(tabulate(rows, headers=headers, tablefmt="grid"))

    def export_to_csv(self, data_type="all", output_dir="exports"):
        """导出数据到CSV文件"""
        import os
        
        # 创建导出目录
        os.makedirs(output_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if data_type in ["all", "stock"]:
            # 导出股票数据
            stocks = self.session.query(StockQuote).all()
            if stocks:
                stock_data = []
                for stock in stocks:
                    stock_data.append({
                        'symbol': stock.symbol,
                        'trade_date': stock.trade_date,
                        'current_price': stock.current_price,
                        'open_price': stock.open_price,
                        'close_price': stock.close_price,
                        'high_price': stock.high_price,
                        'low_price': stock.low_price,
                        'volume': stock.volume,
                        'amount': stock.amount,
                        'change_rate': stock.change_rate,
                        'change_amount': stock.change_amount,
                        'created_at': stock.created_at
                    })

                df = pd.DataFrame(stock_data)
                filename = f"{output_dir}/stock_data_{timestamp}.csv"
                df.to_csv(filename, index=False, encoding='utf-8-sig')
                print(f"✅ 股票数据已导出到: {filename}")
        
        if data_type in ["all", "plate"]:
            # 导出板块数据
            plates = self.session.query(PlateInfo).all()
            if plates:
                plate_data = []
                for plate in plates:
                    plate_data.append({
                        'plate_code': plate.plate_code,
                        'plate_name': plate.plate_name,
                        'plate_type': plate.plate_type,
                        'trade_date': plate.trade_date,
                        'change_rate': plate.change_rate,
                        'created_at': plate.created_at
                    })
                
                df = pd.DataFrame(plate_data)
                filename = f"{output_dir}/plate_data_{timestamp}.csv"
                df.to_csv(filename, index=False, encoding='utf-8-sig')
                print(f"✅ 板块数据已导出到: {filename}")
        
        if data_type in ["all", "longhubang"]:
            # 导出龙虎榜数据
            lhb_data = self.session.query(LongHuBang).all()
            if lhb_data:
                lhb_list = []
                for lhb in lhb_data:
                    lhb_list.append({
                        'symbol': lhb.symbol,
                        'trade_date': lhb.trade_date,
                        'reason': lhb.reason,
                        'buy_amount': lhb.buy_amount,
                        'sell_amount': lhb.sell_amount,
                        'net_amount': lhb.net_amount,
                        'department': lhb.department,
                        'created_at': lhb.created_at
                    })

                df = pd.DataFrame(lhb_list)
                filename = f"{output_dir}/longhubang_data_{timestamp}.csv"
                df.to_csv(filename, index=False, encoding='utf-8-sig')
                print(f"✅ 龙虎榜数据已导出到: {filename}")

        if data_type in ["all", "plate_strength"]:
            # 导出板块强度数据
            strength_data = self.session.query(PlateStrength).all()
            if strength_data:
                strength_list = []
                for strength in strength_data:
                    strength_list.append({
                        'plate_code': strength.plate_code,
                        'plate_name': strength.plate_name,
                        'strength': strength.strength,
                        'rise_speed': strength.rise_speed,
                        'turnover': strength.turnover,
                        'main_net_amount': strength.main_net_amount,
                        'main_buy_amount': strength.main_buy_amount,
                        'main_sell_amount': strength.main_sell_amount,
                        'volume_ratio': strength.volume_ratio,
                        'market_cap': strength.market_cap,
                        'trade_date': strength.trade_date,
                        'created_at': strength.created_at
                    })

                df = pd.DataFrame(strength_list)
                filename = f"{output_dir}/plate_strength_data_{timestamp}.csv"
                df.to_csv(filename, index=False, encoding='utf-8-sig')
                print(f"✅ 板块强度数据已导出到: {filename}")
    
    def close(self):
        """关闭数据库连接"""
        self.session.close()

def main():
    """主函数 - 交互式数据查看"""
    viewer = DataViewer()
    
    try:
        while True:
            print("\n" + "="*50)
            print("🔍 开盘啦数据查看工具")
            print("="*50)
            print("1. 数据概览")
            print("2. 查看股票数据")
            print("3. 查看板块数据")
            print("4. 查看龙虎榜数据")
            print("5. 查看板块强度数据")
            print("6. 导出数据到CSV")
            print("0. 退出")
            print("-"*50)

            choice = input("请选择操作 (0-6): ").strip()
            
            if choice == "0":
                print("👋 再见！")
                break
            elif choice == "1":
                viewer.show_data_summary()
            elif choice == "2":
                viewer.show_stock_data()
            elif choice == "3":
                viewer.show_plate_data()
            elif choice == "4":
                viewer.show_longhubang_data()
            elif choice == "5":
                viewer.show_plate_strength_data()
            elif choice == "6":
                print("选择导出类型:")
                print("1. 全部数据")
                print("2. 仅股票数据")
                print("3. 仅板块数据")
                print("4. 仅龙虎榜数据")
                print("5. 仅板块强度数据")
                export_choice = input("请选择 (1-5): ").strip()

                export_map = {
                    "1": "all",
                    "2": "stock",
                    "3": "plate",
                    "4": "longhubang",
                    "5": "plate_strength"
                }
                
                if export_choice in export_map:
                    viewer.export_to_csv(export_map[export_choice])
                else:
                    print("❌ 无效选择")
            else:
                print("❌ 无效选择，请重新输入")
            
            input("\n按回车键继续...")
    
    except KeyboardInterrupt:
        print("\n👋 用户中断，再见！")
    finally:
        viewer.close()

if __name__ == "__main__":
    main()
