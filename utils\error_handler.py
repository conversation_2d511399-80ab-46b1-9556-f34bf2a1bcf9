"""
错误处理和重试机制
"""
import time
import functools
from typing import Callable, Any, Optional, Type, Tuple
from retrying import retry
import requests

from utils.logger import setup_logger
from utils.exceptions import APIException, NetworkException, DataParseException

logger = setup_logger(__name__)

def retry_on_exception(
    max_attempts: int = 3,
    delay: float = 1.0,
    backoff: float = 2.0,
    exceptions: Tuple[Type[Exception], ...] = (Exception,)
):
    """
    重试装饰器
    
    Args:
        max_attempts: 最大重试次数
        delay: 初始延迟时间(秒)
        backoff: 延迟倍数
        exceptions: 需要重试的异常类型
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_attempts - 1:
                        logger.warning(
                            f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {str(e)}, "
                            f"{current_delay:.1f}秒后重试"
                        )
                        time.sleep(current_delay)
                        current_delay *= backoff
                    else:
                        logger.error(f"函数 {func.__name__} 重试 {max_attempts} 次后仍然失败")
                except Exception as e:
                    # 不在重试范围内的异常直接抛出
                    logger.error(f"函数 {func.__name__} 发生不可重试异常: {str(e)}")
                    raise
            
            # 所有重试都失败，抛出最后一个异常
            raise last_exception
        
        return wrapper
    return decorator

def handle_api_error(response: requests.Response, url: str) -> None:
    """
    处理API错误响应
    
    Args:
        response: HTTP响应对象
        url: 请求URL
        
    Raises:
        APIException: API异常
    """
    if response.status_code == 200:
        return
    
    error_msg = f"API请求失败，状态码: {response.status_code}, URL: {url}"
    
    # 根据状态码分类处理
    if response.status_code == 400:
        error_msg += " - 请求参数错误"
    elif response.status_code == 401:
        error_msg += " - 认证失败"
    elif response.status_code == 403:
        error_msg += " - 访问被拒绝"
    elif response.status_code == 404:
        error_msg += " - 资源不存在"
    elif response.status_code == 429:
        error_msg += " - 请求频率过高"
    elif response.status_code >= 500:
        error_msg += " - 服务器内部错误"
    
    logger.error(error_msg)
    raise APIException(error_msg, response.status_code, response.text)

def handle_network_error(e: Exception, url: str) -> None:
    """
    处理网络错误
    
    Args:
        e: 异常对象
        url: 请求URL
        
    Raises:
        NetworkException: 网络异常
    """
    error_msg = f"网络请求失败: {str(e)}, URL: {url}"
    
    if isinstance(e, requests.exceptions.Timeout):
        error_msg += " - 请求超时"
    elif isinstance(e, requests.exceptions.ConnectionError):
        error_msg += " - 连接错误"
    elif isinstance(e, requests.exceptions.SSLError):
        error_msg += " - SSL错误"
    
    logger.error(error_msg)
    raise NetworkException(error_msg, url)

def safe_execute(func: Callable, *args, default_return=None, **kwargs) -> Any:
    """
    安全执行函数，捕获所有异常
    
    Args:
        func: 要执行的函数
        *args: 位置参数
        default_return: 异常时的默认返回值
        **kwargs: 关键字参数
        
    Returns:
        函数执行结果或默认值
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        logger.error(f"函数 {func.__name__} 执行异常: {str(e)}")
        return default_return

def validate_data(data: Any, required_fields: list = None) -> bool:
    """
    验证数据完整性
    
    Args:
        data: 要验证的数据
        required_fields: 必需字段列表
        
    Returns:
        bool: 数据是否有效
        
    Raises:
        DataParseException: 数据验证失败
    """
    if data is None:
        raise DataParseException("数据为空")
    
    if required_fields and isinstance(data, dict):
        missing_fields = []
        for field in required_fields:
            if field not in data:
                missing_fields.append(field)
        
        if missing_fields:
            raise DataParseException(f"缺少必需字段: {missing_fields}")
    
    return True

class ErrorCollector:
    """错误收集器"""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
    
    def add_error(self, error: str, context: dict = None):
        """添加错误"""
        error_info = {
            'message': error,
            'context': context or {},
            'timestamp': time.time()
        }
        self.errors.append(error_info)
        logger.error(f"收集错误: {error}")
    
    def add_warning(self, warning: str, context: dict = None):
        """添加警告"""
        warning_info = {
            'message': warning,
            'context': context or {},
            'timestamp': time.time()
        }
        self.warnings.append(warning_info)
        logger.warning(f"收集警告: {warning}")
    
    def has_errors(self) -> bool:
        """是否有错误"""
        return len(self.errors) > 0
    
    def has_warnings(self) -> bool:
        """是否有警告"""
        return len(self.warnings) > 0
    
    def get_summary(self) -> dict:
        """获取错误摘要"""
        return {
            'error_count': len(self.errors),
            'warning_count': len(self.warnings),
            'errors': self.errors,
            'warnings': self.warnings
        }
    
    def clear(self):
        """清空错误记录"""
        self.errors.clear()
        self.warnings.clear()

# 全局错误收集器实例
global_error_collector = ErrorCollector()

def circuit_breaker(failure_threshold: int = 5, recovery_timeout: int = 60):
    """
    熔断器装饰器
    
    Args:
        failure_threshold: 失败阈值
        recovery_timeout: 恢复超时时间(秒)
    """
    def decorator(func: Callable) -> Callable:
        func._failure_count = 0
        func._last_failure_time = 0
        func._is_open = False
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            current_time = time.time()
            
            # 检查熔断器状态
            if func._is_open:
                if current_time - func._last_failure_time > recovery_timeout:
                    # 尝试恢复
                    func._is_open = False
                    func._failure_count = 0
                    logger.info(f"熔断器恢复: {func.__name__}")
                else:
                    raise Exception(f"熔断器开启，函数 {func.__name__} 暂时不可用")
            
            try:
                result = func(*args, **kwargs)
                # 成功执行，重置失败计数
                func._failure_count = 0
                return result
            except Exception as e:
                func._failure_count += 1
                func._last_failure_time = current_time
                
                if func._failure_count >= failure_threshold:
                    func._is_open = True
                    logger.error(f"熔断器开启: {func.__name__}, 失败次数: {func._failure_count}")
                
                raise
        
        return wrapper
    return decorator
